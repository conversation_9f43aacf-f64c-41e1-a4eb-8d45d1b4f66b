spring.application.name=lingetic
server.port=8000

app.environment=${ENVIRONMENT}

spring.web.cors.allowed-origins=${FRONTEND_URL}
spring.main.allow-circular-references=true

spring.datasource.url=jdbc:postgresql://${DATABASE_HOST}:${DATABASE_PORT}/${DATABASE_NAME}?sslmode=${DATABASE_SSLMODE}
spring.datasource.username=${DATABASE_USERNAME}
spring.datasource.password=${DATABASE_PASSWORD}
spring.datasource.driver-class-name=org.postgresql.Driver

spring.flyway.enabled=true

clerk.apiKey=${CLERK_API_KEY}
clerk.jwksPublicKey=${CLERK_JWKS_PUBLIC_KEY}

sentry.dsn=https://<EMAIL>/4508942592770128
sentry.send-default-pii=true
sentry.environment=${ENVIRONMENT}

logging.level.root=${SPRING_LOGGING_LEVEL}
debug=${SPRING_DEBUG}
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.security.servlet.UserDetailsServiceAutoConfiguration

spring.rabbitmq.uri=${RABBITMQ_PROTOCOL}://${RABBITMQ_USERNAME}:${RABBITMQ_PASSWORD}@${RABBITMQ_HOST}:${RABBITMQ_PORT}${RABBITMQ_VHOST}
