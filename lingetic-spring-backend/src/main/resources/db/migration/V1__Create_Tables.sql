CREATE TABLE IF NOT EXISTS questions (
    id UUID PRIMARY KEY,
    question_type TEXT NOT NULL CHECK (question_type in ('FillInTheBlanks')),
    language TEXT NOT NULL CHECK (language in ('English', 'Turkish')),
    difficulty SMALLINT NOT NULL,
    question_list_id UUID NOT NULL,
    question_type_specific_data JSONB NOT NULL
);

CREATE TABLE IF NOT EXISTS question_reviews (
    id UUID PRIMARY KEY,
    question_id UUID NOT NULL REFERENCES questions(id) ON DELETE CASCADE,
    user_id TEXT NOT NULL,
    language TEXT NOT NULL CHECK (language in ('English', 'Turkish')),
    repetitions SMALLINT NOT NULL DEFAULT 0,
    ease_factor REAL NOT NULL DEFAULT 2.5 CHECK (ease_factor >= 1.29),
    interval SMALLINT NOT NULL DEFAULT 0 CHECK (interval >= 0),
    next_review_instant TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (question_id, user_id)
);