{"reflection": [{"type": "[I"}, {"type": "[J"}, {"type": "[Lcom.fasterxml.jackson.databind.deser.BeanDeserializerModifier;"}, {"type": "[Lcom.fasterxml.jackson.databind.deser.Deserializers;"}, {"type": "[Lcom.fasterxml.jackson.databind.deser.KeyDeserializers;"}, {"type": "[Lcom.fasterxml.jackson.databind.deser.ValueInstantiators;"}, {"type": "[Lcom.fasterxml.jackson.databind.ser.BeanSerializerModifier;"}, {"type": "[Lcom.fasterxml.jackson.databind.ser.Serializers;"}, {"type": "[Lcom.munetmo.lingetic.LanguageTestService.Entities.Questions.FillInTheBlanksQuestion$WordExplanation;"}, {"type": "[Ljava.lang.Class;"}, {"type": "[Ljava.lang.Object;"}, {"type": "[Ljava.lang.String;"}, {"type": "[Ljava.lang.annotation.Annotation;"}, {"type": "[Ljava.sql.Statement;"}, {"type": "[Lorg.springframework.core.annotation.AnnotationAttributes;"}, {"type": "[Lorg.springframework.util.ConcurrentReferenceHashMap$Segment;"}, {"type": "[Lorg.springframework.web.bind.annotation.RequestMethod;"}, {"type": "[Z"}, {"type": "boolean"}, {"type": "ch.qos.logback.classic.BasicConfigurator", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "ch.qos.logback.classic.LoggerContext"}, {"type": "ch.qos.logback.classic.joran.SerializedModelConfigurator", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "ch.qos.logback.classic.spi.LogbackServiceProvider"}, {"type": "ch.qos.logback.classic.util.DefaultJoranConfigurator", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "co.elastic.clients.elasticsearch.ElasticsearchClient"}, {"type": "co.elastic.clients.transport.ElasticsearchTransport"}, {"type": "com.couchbase.client.java.Bucket"}, {"type": "com.couchbase.client.java.Cluster"}, {"type": "com.datastax.oss.driver.api.core.CqlSession"}, {"type": "com.fasterxml.jackson.annotation.JacksonAnnotation"}, {"type": "com.fasterxml.jackson.core.JsonGenerator"}, {"type": "com.fasterxml.jackson.core.ObjectCodec", "allDeclaredFields": true}, {"type": "com.fasterxml.jackson.core.TreeCodec", "allDeclaredFields": true}, {"type": "com.fasterxml.jackson.core.Versioned"}, {"type": "com.fasterxml.jackson.databind.JsonDeserializer", "allDeclaredFields": true}, {"type": "com.fasterxml.jackson.databind.Module", "allDeclaredFields": true}, {"type": "com.fasterxml.jackson.databind.ObjectMapper", "allDeclaredFields": true}, {"type": "com.fasterxml.jackson.databind.annotation.JsonDeserialize"}, {"type": "com.fasterxml.jackson.databind.deser.NullValueProvider"}, {"type": "com.fasterxml.jackson.databind.ext.Java7SupportImpl", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "com.fasterxml.jackson.databind.module.SimpleModule", "allDeclaredFields": true}, {"type": "com.fasterxml.jackson.dataformat.cbor.CBORFactory"}, {"type": "com.fasterxml.jackson.dataformat.smile.SmileFactory"}, {"type": "com.fasterxml.jackson.dataformat.xml.XmlMapper"}, {"type": "com.fasterxml.jackson.dataformat.yaml.YAMLFactory"}, {"type": "com.fasterxml.jackson.datatype.jdk8.Jdk8Module", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "com.fasterxml.jackson.datatype.jsr310.JavaTimeModule", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "com.fasterxml.jackson.module.paramnames.ParameterNamesModule", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "com.github.benmanes.caffeine.cache.Caffeine"}, {"type": "com.google.gson.Gson"}, {"type": "com.hazelcast.core.HazelcastInstance"}, {"type": "com.mongodb.client.MongoClient"}, {"type": "com.mongodb.reactivestreams.client.MongoClient"}, {"type": "com.munetmo.lingetic.HealthService.infra.HTTP.HealthServiceController", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "wakeup", "parameterTypes": []}]}, {"type": "com.munetmo.lingetic.LanguageService.Entities.Language", "allDeclaredFields": true, "fields": [{"name": "Swedish"}]}, {"type": "com.munetmo.lingetic.LanguageService.Entities.LanguageEditor"}, {"type": "com.munetmo.lingetic.LanguageService.Entities.Token", "allDeclaredFields": true, "methods": [{"name": "sequenceNumber", "parameterTypes": []}, {"name": "type", "parameterTypes": []}, {"name": "value", "parameterTypes": []}]}, {"type": "com.munetmo.lingetic.LanguageService.Entities.TokenType", "allDeclaredFields": true}, {"type": "com.munetmo.lingetic.LanguageService.infra.HTTP.LanguageServiceController", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "tokenize", "parameterTypes": ["com.munetmo.lingetic.LanguageService.Entities.Language", "java.lang.String"]}]}, {"type": "com.munetmo.lingetic.LanguageTestService.DTOs.Attempt.AttemptResponses.AttemptResponse"}, {"type": "com.munetmo.lingetic.LanguageTestService.DTOs.Attempt.AttemptResponses.FillInTheBlanksAttemptResponse", "allDeclaredFields": true, "methods": [{"name": "getAttemptStatus", "parameterTypes": []}, {"name": "getCorrectAnswer", "parameterTypes": []}, {"name": "getExplanation", "parameterTypes": []}, {"name": "getQuestionType", "parameterTypes": []}, {"name": "getSourceWordExplanations", "parameterTypes": []}]}, {"type": "com.munetmo.lingetic.LanguageTestService.DTOs.Attempt.AttemptResponses.SourceToTargetTranslationAttemptResponse", "allDeclaredFields": true, "methods": [{"name": "getAttemptStatus", "parameterTypes": []}, {"name": "getCorrectAnswer", "parameterTypes": []}, {"name": "getQuestionType", "parameterTypes": []}]}, {"type": "com.munetmo.lingetic.LanguageTestService.DTOs.Question.FillInTheBlanksQuestionDTO", "allDeclaredFields": true, "methods": [{"name": "getFullTextDigest", "parameterTypes": []}, {"name": "getHint", "parameterTypes": []}, {"name": "getID", "parameterTypes": []}, {"name": "getLanguage", "parameterTypes": []}, {"name": "getQuestionType", "parameterTypes": []}, {"name": "getSentenceID", "parameterTypes": []}, {"name": "getText", "parameterTypes": []}]}, {"type": "com.munetmo.lingetic.LanguageTestService.DTOs.Question.QuestionDTO"}, {"type": "com.munetmo.lingetic.LanguageTestService.DTOs.Question.SourceToTargetTranslationDTO", "allDeclaredFields": true, "methods": [{"name": "getID", "parameterTypes": []}, {"name": "getLanguage", "parameterTypes": []}, {"name": "getQuestionType", "parameterTypes": []}, {"name": "getTranslation", "parameterTypes": []}]}, {"type": "com.munetmo.lingetic.LanguageTestService.DTOs.TaskPayloads.GenericTaskPayloadWrapper", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": ["java.lang.String", "java.lang.Object"]}]}, {"type": "com.munetmo.lingetic.LanguageTestService.DTOs.TaskPayloads.QuestionReviewProcessingPayload", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": ["java.lang.String", "java.lang.String", "com.munetmo.lingetic.LanguageTestService.Entities.AttemptStatus"]}, {"name": "questionId", "parameterTypes": []}, {"name": "status", "parameterTypes": []}, {"name": "userId", "parameterTypes": []}]}, {"type": "com.munetmo.lingetic.LanguageTestService.DTOs.TaskPayloads.SentenceReviewProcessingPayload", "allDeclaredFields": true, "methods": [{"name": "sentenceId", "parameterTypes": []}, {"name": "status", "parameterTypes": []}, {"name": "userId", "parameterTypes": []}]}, {"type": "com.munetmo.lingetic.LanguageTestService.Entities.AttemptStatus", "allDeclaredFields": true}, {"type": "com.munetmo.lingetic.LanguageTestService.Entities.Language", "allDeclaredFields": true}, {"type": "com.munetmo.lingetic.LanguageTestService.Entities.QuestionList", "allDeclaredFields": true, "methods": [{"name": "getID", "parameterTypes": []}, {"name": "getLanguage", "parameterTypes": []}, {"name": "getName", "parameterTypes": []}]}, {"type": "com.munetmo.lingetic.LanguageTestService.Entities.Questions.FillInTheBlanksQuestion$WordExplanation", "allDeclaredFields": true, "methods": [{"name": "comment", "parameterTypes": []}, {"name": "properties", "parameterTypes": []}, {"name": "sequenceNumber", "parameterTypes": []}, {"name": "startIndex", "parameterTypes": []}, {"name": "word", "parameterTypes": []}]}, {"type": "com.munetmo.lingetic.LanguageTestService.Entities.Questions.QuestionType", "allDeclaredFields": true}, {"type": "com.munetmo.lingetic.LanguageTestService.Entities.WordExplanation", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": ["int", "java.lang.String", "java.util.List", "java.lang.String"]}, {"name": "comment", "parameterTypes": []}, {"name": "properties", "parameterTypes": []}, {"name": "startIndex", "parameterTypes": []}, {"name": "word", "parameterTypes": []}]}, {"type": "com.munetmo.lingetic.LanguageTestService.Repositories.QuestionListRepository"}, {"type": "com.munetmo.lingetic.LanguageTestService.Repositories.QuestionRepository"}, {"type": "com.munetmo.lingetic.LanguageTestService.Repositories.QuestionReviewRepository"}, {"type": "com.munetmo.lingetic.LanguageTestService.Repositories.SentenceRepository"}, {"type": "com.munetmo.lingetic.LanguageTestService.Repositories.SentenceReviewRepository"}, {"type": "com.munetmo.lingetic.LanguageTestService.UseCases.AttemptQuestionUseCase", "allDeclaredFields": true}, {"type": "com.munetmo.lingetic.LanguageTestService.UseCases.GetQuestionListsForLanguageUseCase", "allDeclaredFields": true}, {"type": "com.munetmo.lingetic.LanguageTestService.UseCases.ReviewQuestionUseCase", "allDeclaredFields": true}, {"type": "com.munetmo.lingetic.LanguageTestService.UseCases.TakeRegularTestUseCase", "allDeclaredFields": true}, {"type": "com.munetmo.lingetic.LanguageTestService.infra.Beans", "allDeclaredFields": true, "methods": [{"name": "attemptQuestionUseCase", "parameterTypes": ["com.munetmo.lingetic.LanguageTestService.Repositories.QuestionRepository", "com.munetmo.lingetic.lib.tasks.TaskQueue", "java.util.concurrent.ExecutorService"]}, {"name": "getQuestionListsForLanguageUseCase", "parameterTypes": ["com.munetmo.lingetic.LanguageTestService.Repositories.QuestionListRepository"]}, {"name": "questionListRepository", "parameterTypes": ["org.springframework.jdbc.core.JdbcTemplate"]}, {"name": "questionRepository", "parameterTypes": ["org.springframework.jdbc.core.JdbcTemplate"]}, {"name": "questionReviewRepository", "parameterTypes": ["org.springframework.jdbc.core.JdbcTemplate"]}, {"name": "reviewQuestionUseCase", "parameterTypes": ["com.munetmo.lingetic.LanguageTestService.Repositories.QuestionRepository", "com.munetmo.lingetic.LanguageTestService.Repositories.QuestionReviewRepository"]}, {"name": "sentenceRepository", "parameterTypes": ["org.springframework.jdbc.core.JdbcTemplate"]}, {"name": "sentenceReviewRepository", "parameterTypes": ["org.springframework.jdbc.core.JdbcTemplate"]}, {"name": "takeRegularTestUseCase", "parameterTypes": ["com.munetmo.lingetic.LanguageTestService.Repositories.QuestionRepository", "com.munetmo.lingetic.LanguageTestService.Repositories.QuestionReviewRepository"]}, {"name": "takeRegularTestUseCase", "parameterTypes": ["com.munetmo.lingetic.LanguageTestService.Repositories.QuestionRepository", "com.munetmo.lingetic.LanguageTestService.Repositories.SentenceReviewRepository", "com.munetmo.lingetic.LanguageTestService.Repositories.SentenceRepository"]}, {"name": "taskSubmitExecutor", "parameterTypes": []}]}, {"type": "com.munetmo.lingetic.LanguageTestService.infra.Beans$$SpringCGLIB$$0", "allDeclaredFields": true, "fields": [{"name": "CGLIB$FACTORY_DATA"}], "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "CGLIB$SET_STATIC_CALLBACKS", "parameterTypes": ["org.springframework.cglib.proxy.Callback[]"]}]}, {"type": "com.munetmo.lingetic.LanguageTestService.infra.Beans$$SpringCGLIB$$FastClass$$0", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Class"]}]}, {"type": "com.munetmo.lingetic.LanguageTestService.infra.Beans$$SpringCGLIB$$FastClass$$1", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Class"]}]}, {"type": "com.munetmo.lingetic.LanguageTestService.infra.Deserializers.AttemptRequestDeserializer", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "com.munetmo.lingetic.LanguageTestService.infra.HTTP.LanguageTestServiceController", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "attemptQuestion", "parameterTypes": ["com.munetmo.lingetic.LanguageTestService.DTOs.Attempt.AttemptRequests.AttemptRequest", "io.jsonwebtoken.<PERSON>lai<PERSON>"]}, {"name": "getQuestionLists", "parameterTypes": ["java.lang.String"]}, {"name": "getQuestions", "parameterTypes": ["java.lang.String", "java.lang.String", "io.jsonwebtoken.<PERSON>lai<PERSON>"]}, {"name": "reviewQuestion", "parameterTypes": ["java.lang.String", "java.lang.String"]}]}, {"type": "com.munetmo.lingetic.LanguageTestService.infra.Repositories.Postgres.QuestionListPostgresRepository", "allDeclaredFields": true}, {"type": "com.munetmo.lingetic.LanguageTestService.infra.Repositories.Postgres.QuestionPostgresRepository", "allDeclaredFields": true}, {"type": "com.munetmo.lingetic.LanguageTestService.infra.Repositories.Postgres.QuestionReviewPostgresRepository", "allDeclaredFields": true}, {"type": "com.munetmo.lingetic.LanguageTestService.infra.Repositories.Postgres.SentencePostgresRepository", "allDeclaredFields": true}, {"type": "com.munetmo.lingetic.LanguageTestService.infra.Repositories.Postgres.SentenceReviewPostgresRepository", "allDeclaredFields": true}, {"type": "com.munetmo.lingetic.LingeticApplication", "allDeclaredFields": true, "methods": [{"name": "main", "parameterTypes": ["java.lang.String[]"]}]}, {"type": "com.munetmo.lingetic.LingeticApplication$$SpringCGLIB$$0", "allDeclaredFields": true, "fields": [{"name": "CGLIB$FACTORY_DATA"}], "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "CGLIB$SET_STATIC_CALLBACKS", "parameterTypes": ["org.springframework.cglib.proxy.Callback[]"]}]}, {"type": "com.munetmo.lingetic.infra.auth.ClerkAuthenticationFilter", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "com.munetmo.lingetic.infra.auth.SecurityConfig", "allDeclaredFields": true, "methods": [{"name": "corsConfigurationSource", "parameterTypes": []}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parameterTypes": ["org.springframework.security.config.annotation.web.builders.HttpSecurity"]}]}, {"type": "com.munetmo.lingetic.infra.auth.SecurityConfig$$SpringCGLIB$$0", "allDeclaredFields": true, "fields": [{"name": "CGLIB$FACTORY_DATA"}], "methods": [{"name": "<init>", "parameterTypes": ["com.munetmo.lingetic.infra.auth.ClerkAuthenticationFilter"]}, {"name": "CGLIB$SET_STATIC_CALLBACKS", "parameterTypes": ["org.springframework.cglib.proxy.Callback[]"]}]}, {"type": "com.munetmo.lingetic.infra.auth.SecurityConfig$$SpringCGLIB$$FastClass$$0", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Class"]}]}, {"type": "com.munetmo.lingetic.infra.auth.SecurityConfig$$SpringCGLIB$$FastClass$$1", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Class"]}]}, {"type": "com.munetmo.lingetic.infra.database.PostgresDatabaseConfig", "allDeclaredFields": true, "methods": [{"name": "dataSource", "parameterTypes": []}, {"name": "jdbcTemplate", "parameterTypes": ["javax.sql.DataSource"]}]}, {"type": "com.munetmo.lingetic.infra.database.PostgresDatabaseConfig$$SpringCGLIB$$0", "allDeclaredFields": true, "fields": [{"name": "CGLIB$FACTORY_DATA"}], "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "CGLIB$SET_STATIC_CALLBACKS", "parameterTypes": ["org.springframework.cglib.proxy.Callback[]"]}]}, {"type": "com.munetmo.lingetic.infra.database.PostgresDatabaseConfig$$SpringCGLIB$$FastClass$$0", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Class"]}]}, {"type": "com.munetmo.lingetic.infra.database.PostgresDatabaseConfig$$SpringCGLIB$$FastClass$$1", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Class"]}]}, {"type": "com.munetmo.lingetic.infra.tasks.RabbitMQConfig", "allDeclaredFields": true, "methods": [{"name": "connectionFactory", "parameterTypes": []}, {"name": "rabbitAdmin", "parameterTypes": ["org.springframework.amqp.rabbit.connection.ConnectionFactory"]}, {"name": "rabbitTemplate", "parameterTypes": ["org.springframework.amqp.rabbit.connection.ConnectionFactory"]}, {"name": "taskQueue", "parameterTypes": ["org.springframework.amqp.rabbit.core.RabbitTemplate", "org.springframework.amqp.rabbit.core.RabbitAdmin", "com.fasterxml.jackson.databind.ObjectMapper"]}]}, {"type": "com.munetmo.lingetic.infra.tasks.RabbitMQConfig$$SpringCGLIB$$0", "allDeclaredFields": true, "fields": [{"name": "CGLIB$FACTORY_DATA"}], "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "CGLIB$SET_STATIC_CALLBACKS", "parameterTypes": ["org.springframework.cglib.proxy.Callback[]"]}]}, {"type": "com.munetmo.lingetic.infra.tasks.RabbitMQConfig$$SpringCGLIB$$FastClass$$0", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Class"]}]}, {"type": "com.munetmo.lingetic.infra.tasks.RabbitMQConfig$$SpringCGLIB$$FastClass$$1", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Class"]}]}, {"type": "com.munetmo.lingetic.infra.tasks.RabbitMQTaskQueue", "allDeclaredFields": true}, {"type": "com.munetmo.lingetic.lib.tasks.Task", "allDeclaredFields": true, "methods": [{"name": "id", "parameterTypes": []}, {"name": "payload", "parameterTypes": []}]}, {"type": "com.munetmo.lingetic.lib.tasks.TaskQueue"}, {"type": "com.p6spy.engine.spy.P6SpyDriver"}, {"type": "com.querydsl.core.Query"}, {"type": "com.rabbitmq.client.Channel", "methods": [{"name": "basicPublish", "parameterTypes": ["java.lang.String", "java.lang.String", "boolean", "com.rabbitmq.client.AMQP$BasicProperties", "byte[]"]}, {"name": "queueDeclare", "parameterTypes": ["java.lang.String", "boolean", "boolean", "boolean", "java.util.Map"]}]}, {"type": "com.rabbitmq.client.ShutdownListener"}, {"type": "com.rometools.rome.feed.WireFeed"}, {"type": "com.samskivert.mustache.Mustache"}, {"type": "com.samskivert.mustache.Template"}, {"type": "com.sendgrid.SendGrid"}, {"type": "com.sun.crypto.provider.AESCipher$General", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "com.sun.crypto.provider.ARCFOURCipher", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "com.sun.crypto.provider.ChaCha20Cipher$ChaCha20Poly1305", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "com.sun.crypto.provider.DESCipher", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "com.sun.crypto.provider.DESedeCipher", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "com.sun.crypto.provider.DHParameters", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "com.sun.crypto.provider.GaloisCounterMode$AESGCM", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "com.sun.crypto.provider.HmacCore$HmacSHA256", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "com.sun.crypto.provider.HmacCore$HmacSHA384", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "com.sun.crypto.provider.PBKDF2Core$HmacSHA1", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "com.sun.crypto.provider.PBKDF2Core$HmacSHA224", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "com.sun.crypto.provider.PBKDF2Core$HmacSHA256", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "com.sun.crypto.provider.PBKDF2Core$HmacSHA384", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "com.sun.crypto.provider.PBKDF2Core$HmacSHA512", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "com.sun.crypto.provider.TlsMasterSecretGenerator", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "com.unboundid.ldap.listener.InMemoryDirectoryServer"}, {"type": "com.worksap.nlp.sudachi.JoinNumericPlugin", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "com.worksap.nlp.sudachi.MeCabOovProviderPlugin", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "com.zaxxer.hikari.HikariConfig", "allDeclaredFields": true, "methods": [{"name": "getCatalog", "parameterTypes": []}, {"name": "getConnectionInitSql", "parameterTypes": []}, {"name": "getConnectionTestQuery", "parameterTypes": []}, {"name": "getConnectionTimeout", "parameterTypes": []}, {"name": "getDataSource", "parameterTypes": []}, {"name": "getDataSourceClassName", "parameterTypes": []}, {"name": "getDataSourceJNDI", "parameterTypes": []}, {"name": "getDataSourceProperties", "parameterTypes": []}, {"name": "getDriverClassName", "parameterTypes": []}, {"name": "getExceptionOverrideClassName", "parameterTypes": []}, {"name": "getHealthCheckProperties", "parameterTypes": []}, {"name": "getHealthCheckRegistry", "parameterTypes": []}, {"name": "getIdleTimeout", "parameterTypes": []}, {"name": "getInitializationFailTimeout", "parameterTypes": []}, {"name": "getJdbcUrl", "parameterTypes": []}, {"name": "getKeepaliveTime", "parameterTypes": []}, {"name": "getLeakDetectionThreshold", "parameterTypes": []}, {"name": "getMaxLifetime", "parameterTypes": []}, {"name": "getMaximumPoolSize", "parameterTypes": []}, {"name": "getMetricRegistry", "parameterTypes": []}, {"name": "getMetricsTrackerFactory", "parameterTypes": []}, {"name": "getMinimumIdle", "parameterTypes": []}, {"name": "getPassword", "parameterTypes": []}, {"name": "getPoolName", "parameterTypes": []}, {"name": "getScheduledExecutor", "parameterTypes": []}, {"name": "getSchema", "parameterTypes": []}, {"name": "getThreadFactory", "parameterTypes": []}, {"name": "getTransactionIsolation", "parameterTypes": []}, {"name": "getUsername", "parameterTypes": []}, {"name": "getValidationTimeout", "parameterTypes": []}, {"name": "isAllowPoolSuspension", "parameterTypes": []}, {"name": "isAutoCommit", "parameterTypes": []}, {"name": "isIsolateInternalQueries", "parameterTypes": []}, {"name": "isReadOnly", "parameterTypes": []}, {"name": "isRegisterMbeans", "parameterTypes": []}]}, {"type": "com.zaxxer.hikari.HikariConfigMXBean"}, {"type": "com.zaxxer.hikari.HikariDataSource", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "com.zaxxer.hikari.pool.PoolBase", "fields": [{"name": "catalog"}]}, {"type": "com.zaxxer.hikari.pool.PoolEntry", "fields": [{"name": "state"}]}, {"type": "freemarker.template.Configuration"}, {"type": "graphql.GraphQL"}, {"type": "graphql.GraphQLError"}, {"type": "groovy.lang.MetaClass"}, {"type": "groovy.text.TemplateEngine"}, {"type": "groovy.text.markup.MarkupTemplateEngine"}, {"type": "io.jsonwebtoken.impl.DefaultClaimsBuilder", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "io.jsonwebtoken.impl.DefaultJwtParserBuilder", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "io.jsonwebtoken.impl.io.StandardCompressionAlgorithms", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "io.jsonwebtoken.impl.security.StandardEncryptionAlgorithms", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "io.jsonwebtoken.impl.security.StandardKeyAlgorithms", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "io.jsonwebtoken.impl.security.StandardKeyOperations", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "io.jsonwebtoken.impl.security.StandardSecureDigestAlgorithms", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "io.jsonwebtoken.jackson.io.JacksonDeserializer"}, {"type": "io.micrometer.common.lang.Nullable"}, {"type": "io.micrometer.context.ContextSnapshot"}, {"type": "io.micrometer.observation.ObservationRegistry"}, {"type": "io.netty.buffer.PooledByteBufAllocator"}, {"type": "io.netty.util.NettyRuntime"}, {"type": "io.opentelemetry.api.OpenTelemetry"}, {"type": "io.r2dbc.proxy.ProxyConnectionFactory"}, {"type": "io.r2dbc.spi.ConnectionFactory"}, {"type": "io.reactivex.rxjava3.core.Flowable"}, {"type": "io.rsocket.RSocket"}, {"type": "io.rsocket.core.RSocketServer"}, {"type": "io.sentry.EventProcessor"}, {"type": "io.sentry.IScopes"}, {"type": "io.sentry.ScopesAdapter", "allDeclaredFields": true, "methods": [{"name": "close", "parameterTypes": []}]}, {"type": "io.sentry.Sentry$OptionsConfiguration"}, {"type": "io.sentry.SentryOptions", "allDeclaredFields": true, "methods": [{"name": "setDsn", "parameterTypes": ["java.lang.String"]}, {"name": "setEnvironment", "parameterTypes": ["java.lang.String"]}, {"name": "setSendDefaultPii", "parameterTypes": ["boolean"]}]}, {"type": "io.sentry.graphql.SentryInstrumentation"}, {"type": "io.sentry.graphql22.SentryInstrumentation"}, {"type": "io.sentry.logback.SentryAppender"}, {"type": "io.sentry.opentelemetry.OtelContextScopesStorage"}, {"type": "io.sentry.opentelemetry.OtelSpanFactory"}, {"type": "io.sentry.opentelemetry.SentryAutoConfigurationCustomizerProvider"}, {"type": "io.sentry.opentelemetry.agent.AgentMarker"}, {"type": "io.sentry.opentelemetry.agent.AgentlessMarker"}, {"type": "io.sentry.opentelemetry.agent.AgentlessSpringMarker"}, {"type": "io.sentry.quartz.SentryJobListener"}, {"type": "io.sentry.spring.boot.jakarta.InAppIncludesResolver", "allDeclaredFields": true}, {"type": "io.sentry.spring.boot.jakarta.SentryAutoConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "io.sentry.spring.boot.jakarta.SentryAutoConfiguration$HubConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "inAppPackagesResolver", "parameterTypes": []}, {"name": "sentry<PERSON>ub", "parameterTypes": ["java.util.List", "io.sentry.spring.boot.jakarta.SentryProperties", "org.springframework.beans.factory.ObjectProvider", "org.springframework.beans.factory.ObjectProvider"]}, {"name": "sentryOptionsConfiguration", "parameterTypes": ["org.springframework.beans.factory.ObjectProvider", "org.springframework.beans.factory.ObjectProvider", "org.springframework.beans.factory.ObjectProvider", "org.springframework.beans.factory.ObjectProvider", "java.util.List", "java.util.List", "org.springframework.beans.factory.ObjectProvider", "org.springframework.beans.factory.ObjectProvider", "io.sentry.spring.boot.jakarta.InAppIncludesResolver"]}]}, {"type": "io.sentry.spring.boot.jakarta.SentryAutoConfiguration$HubConfiguration$ContextTagsEventProcessorConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "contextTagsEventProcessor", "parameterTypes": ["io.sentry.SentryOptions"]}]}, {"type": "io.sentry.spring.boot.jakarta.SentryAutoConfiguration$HubConfiguration$SentryPerformanceRestTemplateConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "sentrySpanRestTemplateCustomizer", "parameterTypes": ["io.sentry.IScopes"]}]}, {"type": "io.sentry.spring.boot.jakarta.SentryAutoConfiguration$HubConfiguration$SentrySpanRestClientConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "sentrySpanRestClientCustomizer", "parameterTypes": ["io.sentry.IScopes"]}]}, {"type": "io.sentry.spring.boot.jakarta.SentryAutoConfiguration$HubConfiguration$SentryWebMvcConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "sentryRequestResolver", "parameterTypes": ["io.sentry.IScopes"]}, {"name": "sentry<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parameterTypes": ["io.sentry.IScopes", "io.sentry.spring.jakarta.SentryRequestResolver", "io.sentry.spring.jakarta.tracing.TransactionNameProvider"]}, {"name": "sentryTracingFilter", "parameterTypes": ["io.sentry.IScopes", "io.sentry.spring.jakarta.tracing.TransactionNameProvider", "io.sentry.spring.boot.jakarta.SentryProperties"]}, {"name": "sentry<PERSON><PERSON><PERSON><PERSON>er", "parameterTypes": ["io.sentry.IScopes", "io.sentry.spring.boot.jakarta.SentryProperties", "java.util.List"]}]}, {"type": "io.sentry.spring.boot.jakarta.SentryAutoConfiguration$HubConfiguration$SentryWebMvcConfiguration$SentryMvcModeConfig", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "sentryExceptionResolver", "parameterTypes": ["io.sentry.IScopes", "io.sentry.spring.jakarta.tracing.TransactionNameProvider", "io.sentry.spring.boot.jakarta.SentryProperties"]}, {"name": "transactionNameProvider", "parameterTypes": []}]}, {"type": "io.sentry.spring.boot.jakarta.SentryAutoConfiguration$HubConfiguration$SentryWebMvcConfiguration$SentrySecurityConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "springSecuritySentryUserProvider", "parameterTypes": ["io.sentry.SentryOptions"]}]}, {"type": "io.sentry.spring.boot.jakarta.SentryAutoConfiguration$SentryTracingCondition", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "io.sentry.spring.boot.jakarta.SentryAutoConfiguration$SpringProfilesEventProcessorConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "springProfilesEventProcessor", "parameterTypes": ["org.springframework.core.env.Environment"]}]}, {"type": "io.sentry.spring.boot.jakarta.SentryLogbackAppenderAutoConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "sentryLogbackInitializer", "parameterTypes": ["io.sentry.spring.boot.jakarta.SentryProperties"]}]}, {"type": "io.sentry.spring.boot.jakarta.SentryLogbackInitializer", "allDeclaredFields": true}, {"type": "io.sentry.spring.boot.jakarta.SentryProperties", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "io.sentry.spring.boot.jakarta.SentrySpanRestClientCustomizer", "allDeclaredFields": true}, {"type": "io.sentry.spring.boot.jakarta.SentrySpanRestTemplateCustomizer", "allDeclaredFields": true}, {"type": "io.sentry.spring.boot.jakarta.SentrySpringVersionChecker", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "io.sentry.spring.jakarta.ContextTagsEventProcessor", "allDeclaredFields": true}, {"type": "io.sentry.spring.jakarta.HttpServletRequestSentryUserProvider", "allDeclaredFields": true}, {"type": "io.sentry.spring.jakarta.SentryExceptionResolver", "allDeclaredFields": true}, {"type": "io.sentry.spring.jakarta.SentryRequestResolver", "allDeclaredFields": true}, {"type": "io.sentry.spring.jakarta.SentrySpringFilter"}, {"type": "io.sentry.spring.jakarta.SentryUserFilter"}, {"type": "io.sentry.spring.jakarta.SentryUserProvider"}, {"type": "io.sentry.spring.jakarta.SentryWebConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "httpServletRequestSentryUserProvider", "parameterTypes": ["io.sentry.SentryOptions"]}]}, {"type": "io.sentry.spring.jakarta.SpringProfilesEventProcessor", "allDeclaredFields": true}, {"type": "io.sentry.spring.jakarta.SpringSecuritySentryUserProvider", "allDeclaredFields": true}, {"type": "io.sentry.spring.jakarta.tracing.SentryTracingFilter"}, {"type": "io.sentry.spring.jakarta.tracing.SpringMvcTransactionNameProvider", "allDeclaredFields": true}, {"type": "io.sentry.spring.jakarta.tracing.TransactionNameProvider"}, {"type": "io.sentry.transport.apache.ApacheHttpClientTransportFactory"}, {"type": "io.smallrye.mutiny.Multi"}, {"type": "io.undertow.Undertow"}, {"type": "io.undertow.websockets.jsr.Bootstrap"}, {"type": "io.vavr.control.Try"}, {"type": "jakarta.activation.MimeType"}, {"type": "jakarta.annotation.ManagedBean"}, {"type": "jakarta.annotation.PostConstruct"}, {"type": "jakarta.annotation.PreDestroy"}, {"type": "jakarta.annotation.Resource"}, {"type": "jakarta.ejb.EJB"}, {"type": "jakarta.ejb.TransactionAttribute"}, {"type": "jakarta.faces.context.FacesContext"}, {"type": "jakarta.inject.Inject"}, {"type": "jakarta.inject.Named"}, {"type": "jakarta.inject.Provider"}, {"type": "jakarta.inject.Qualifier"}, {"type": "jakarta.jms.ConnectionFactory"}, {"type": "jakarta.jms.Message"}, {"type": "jakarta.json.bind.Jsonb"}, {"type": "jakarta.persistence.EntityManager"}, {"type": "jakarta.persistence.EntityManagerFactory"}, {"type": "jakarta.persistence.PersistenceContext"}, {"type": "jakarta.servlet.Filter"}, {"type": "jakarta.servlet.GenericFilter"}, {"type": "jakarta.servlet.GenericServlet", "allDeclaredFields": true}, {"type": "jakarta.servlet.MultipartConfigElement", "allDeclaredFields": true}, {"type": "jakarta.servlet.Servlet"}, {"type": "jakarta.servlet.ServletConfig"}, {"type": "jakarta.servlet.ServletContext"}, {"type": "jakarta.servlet.ServletRegistration"}, {"type": "jakarta.servlet.ServletRequest"}, {"type": "jakarta.servlet.http.HttpServlet", "allDeclaredFields": true}, {"type": "jakarta.servlet.jsp.jstl.core.Config"}, {"type": "jakarta.transaction.Transaction"}, {"type": "jakarta.transaction.TransactionManager"}, {"type": "jakarta.transaction.Transactional"}, {"type": "jakarta.validation.Validator"}, {"type": "jakarta.validation.executable.ExecutableValidator"}, {"type": "jakarta.websocket.server.ServerContainer"}, {"type": "jakarta.xml.bind.Binder"}, {"type": "jakarta.xml.ws.WebServiceRef"}, {"type": "java.io.Closeable"}, {"type": "java.io.Console", "methods": [{"name": "isTerminal", "parameterTypes": []}]}, {"type": "java.io.FilePermission"}, {"type": "java.io.Serializable"}, {"type": "java.lang.AutoCloseable"}, {"type": "java.lang.Bo<PERSON>an"}, {"type": "java.lang.Class", "methods": [{"name": "getRecordComponents", "parameterTypes": []}]}, {"type": "java.lang.ClassLoader", "fields": [{"name": "classLoaderValueMap"}]}, {"type": "java.lang.Comparable"}, {"type": "java.lang.Iterable"}, {"type": "java.lang.Module"}, {"type": "java.lang.Object"}, {"type": "java.lang.Record", "allDeclaredFields": true}, {"type": "java.lang.RuntimePermission"}, {"type": "java.lang.SecurityManager", "methods": [{"name": "checkPermission", "parameterTypes": ["java.security.Permission"]}]}, {"type": "java.lang.Shutdown"}, {"type": "java.lang.String"}, {"type": "java.lang.System", "methods": [{"name": "getSecurityManager", "parameterTypes": []}]}, {"type": "java.lang.Terminator$1"}, {"type": "java.lang.Thread", "fields": [{"name": "threadLocalRandomProbe"}]}, {"type": "java.lang.Thread$Builder"}, {"type": "java.lang.WrongThreadException"}, {"type": "java.lang.annotation.Documented"}, {"type": "java.lang.annotation.Inherited"}, {"type": "java.lang.annotation.Repeatable"}, {"type": "java.lang.annotation.Retention"}, {"type": "java.lang.annotation.Target"}, {"type": "java.lang.constant.Constable"}, {"type": "java.lang.invoke.TypeDescriptor$OfField"}, {"type": "java.lang.reflect.AnnotatedElement"}, {"type": "java.lang.reflect.GenericArrayType", "methods": [{"name": "getGenericComponentType", "parameterTypes": []}]}, {"type": "java.lang.reflect.GenericDeclaration"}, {"type": "java.lang.reflect.Method"}, {"type": "java.lang.reflect.ParameterizedType", "methods": [{"name": "getActualTypeArguments", "parameterTypes": []}, {"name": "getRawType", "parameterTypes": []}]}, {"type": "java.lang.reflect.RecordComponent", "methods": [{"name": "getName", "parameterTypes": []}, {"name": "getType", "parameterTypes": []}]}, {"type": "java.lang.reflect.Type"}, {"type": "java.lang.reflect.TypeVariable", "methods": [{"name": "getBounds", "parameterTypes": []}]}, {"type": "java.lang.reflect.WildcardType", "methods": [{"name": "getLowerBounds", "parameterTypes": []}, {"name": "getUpperBounds", "parameterTypes": []}]}, {"type": "java.net.NetPermission"}, {"type": "java.net.SocketPermission"}, {"type": "java.net.URLPermission", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.String", "java.lang.String"]}]}, {"type": "java.net.UnixDomainSocketAddress"}, {"type": "java.net.http.HttpClient"}, {"type": "java.nio.channels.ServerSocketChannel"}, {"type": "java.nio.channels.SocketChannel"}, {"type": "java.security.AccessController", "methods": [{"name": "doPrivileged", "parameterTypes": ["java.security.PrivilegedExceptionAction"]}]}, {"type": "java.security.AlgorithmParametersSpi"}, {"type": "java.security.AllPermission"}, {"type": "java.security.KeyStoreSpi"}, {"type": "java.security.SecurityPermission"}, {"type": "java.security.interfaces.EdECKey"}, {"type": "java.security.interfaces.RSAPrivateKey"}, {"type": "java.security.interfaces.RSAPublicKey"}, {"type": "java.security.interfaces.XECKey"}, {"type": "java.security.spec.NamedParameterSpec"}, {"type": "java.sql.Driver"}, {"type": "java.sql.<PERSON><PERSON><PERSON><PERSON>"}, {"type": "java.sql.Types", "allPublicFields": true}, {"type": "java.sql.Wrapper"}, {"type": "java.text.ListFormat"}, {"type": "java.util.AbstractCollection", "allDeclaredFields": true}, {"type": "java.util.AbstractMap", "allDeclaredFields": true}, {"type": "java.util.ArrayList"}, {"type": "java.util.Collection"}, {"type": "java.util.Enumeration"}, {"type": "java.util.EventListener"}, {"type": "java.util.HashSet"}, {"type": "java.util.ImmutableCollections$AbstractImmutableCollection", "allDeclaredFields": true}, {"type": "java.util.ImmutableCollections$AbstractImmutableList", "allDeclaredFields": true}, {"type": "java.util.ImmutableCollections$AbstractImmutableMap", "allDeclaredFields": true}, {"type": "java.util.ImmutableCollections$ListN", "allDeclaredFields": true}, {"type": "java.util.ImmutableCollections$Map1", "allDeclaredFields": true}, {"type": "java.util.List"}, {"type": "java.util.Map"}, {"type": "java.util.PropertyPermission"}, {"type": "java.util.RandomAccess"}, {"type": "java.util.Set"}, {"type": "java.util.concurrent.AbstractExecutorService"}, {"type": "java.util.concurrent.Callable"}, {"type": "java.util.concurrent.Executor"}, {"type": "java.util.concurrent.ExecutorService"}, {"type": "java.util.concurrent.ThreadPoolExecutor", "methods": [{"name": "shutdown", "parameterTypes": []}]}, {"type": "java.util.concurrent.atomic.AtomicBoolean", "fields": [{"name": "value"}]}, {"type": "java.util.concurrent.atomic.Striped64", "fields": [{"name": "base"}, {"name": "cellsBusy"}]}, {"type": "java.util.concurrent.atomic.Striped64$Cell", "fields": [{"name": "value"}]}, {"type": "java.util.logging.LogManager"}, {"type": "java.util.logging.SimpleFormatter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "javax.annotation.ManagedBean"}, {"type": "javax.annotation.PostConstruct"}, {"type": "javax.annotation.PreDestroy"}, {"type": "javax.annotation.Resource"}, {"type": "javax.cache.Caching"}, {"type": "javax.inject.Inject"}, {"type": "javax.inject.Named"}, {"type": "javax.inject.Qualifier"}, {"type": "javax.money.MonetaryAmount"}, {"type": "javax.naming.InitialContext"}, {"type": "javax.naming.ldap.LdapContext"}, {"type": "javax.security.auth.Subject"}, {"type": "javax.smartcardio.CardPermission"}, {"type": "javax.sql.CommonDataSource"}, {"type": "javax.sql.DataSource"}, {"type": "javax.sql.XADataSource"}, {"type": "jdk.crac.management.CRaCMXBean"}, {"type": "jdk.internal.loader.ClassLoaders$AppClassLoader"}, {"type": "jdk.internal.loader.ClassLoaders$PlatformClassLoader"}, {"type": "jdk.internal.misc.Signal$1"}, {"type": "jdk.internal.misc.Unsafe"}, {"type": "jdk.internal.reflect.DirectMethodHandleAccessor"}, {"type": "kotlin.Metadata"}, {"type": "kotlinx.coroutines.reactor.MonoKt"}, {"type": "kotlinx.serialization.cbor.Cbor"}, {"type": "kotlinx.serialization.json.Json"}, {"type": "kotlinx.serialization.protobuf.ProtoBuf"}, {"type": "liquibase.change.DatabaseChange"}, {"type": "oracle.jdbc.OracleConnection"}, {"type": "oracle.ucp.jdbc.PoolDataSource"}, {"type": "oracle.ucp.jdbc.PoolDataSourceImpl"}, {"type": "org.aopalliance.aop.Advice"}, {"type": "org.aopalliance.intercept.Interceptor"}, {"type": "org.aopalliance.intercept.MethodInterceptor"}, {"type": "org.apache.catalina.authenticator.AuthenticatorBase"}, {"type": "org.apache.catalina.connector.CoyoteAdapter"}, {"type": "org.apache.catalina.core.ApplicationContextFacade"}, {"type": "org.apache.catalina.core.ApplicationFilterChain"}, {"type": "org.apache.catalina.core.AprLifecycleListener"}, {"type": "org.apache.catalina.core.StandardContextValve"}, {"type": "org.apache.catalina.core.StandardEngineValve"}, {"type": "org.apache.catalina.core.StandardHostValve"}, {"type": "org.apache.catalina.core.StandardWrapperValve"}, {"type": "org.apache.catalina.loader.JdbcLeakPrevention", "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "clearJdbcDriverRegistrations", "parameterTypes": []}]}, {"type": "org.apache.catalina.startup.Tomcat"}, {"type": "org.apache.catalina.util.CharsetMapper", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.apache.catalina.valves.ErrorReportValve"}, {"type": "org.apache.commons.dbcp2.BasicDataSource"}, {"type": "org.apache.commons.logging.LogFactory"}, {"type": "org.apache.commons.logging.LogFactoryService", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.apache.commons.logging.impl.WeakHashtable", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.apache.coyote.AbstractProcessorLight"}, {"type": "org.apache.coyote.AbstractProtocol", "methods": [{"name": "get<PERSON><PERSON><PERSON>", "parameterTypes": []}, {"name": "getLocalPort", "parameterTypes": []}, {"name": "getProperty", "parameterTypes": ["java.lang.String"]}, {"name": "setPort", "parameterTypes": ["int"]}, {"name": "setProperty", "parameterTypes": ["java.lang.String", "java.lang.String"]}]}, {"type": "org.apache.coyote.AbstractProtocol$ConnectionHandler"}, {"type": "org.apache.coyote.UpgradeProtocol"}, {"type": "org.apache.coyote.http11.AbstractHttp11Protocol", "methods": [{"name": "isSSLEnabled", "parameterTypes": []}]}, {"type": "org.apache.coyote.http11.Http11InputBuffer"}, {"type": "org.apache.coyote.http11.Http11NioProtocol"}, {"type": "org.apache.coyote.http11.Http11Processor"}, {"type": "org.apache.derby.jdbc.EmbeddedDriver"}, {"type": "org.apache.hc.client5.http.impl.classic.HttpClients"}, {"type": "org.apache.jasper.compiler.JspConfig"}, {"type": "org.apache.jasper.servlet.JspServlet"}, {"type": "org.apache.logging.log4j.core.impl.Log4jContextFactory"}, {"type": "org.apache.logging.log4j.spi.ExtendedLogger"}, {"type": "org.apache.logging.slf4j.SLF4JProvider"}, {"type": "org.apache.pulsar.client.api.PulsarClient"}, {"type": "org.apache.tomcat.jdbc.pool.DataSource"}, {"type": "org.apache.tomcat.jdbc.pool.DataSourceProxy"}, {"type": "org.apache.tomcat.jni.Library"}, {"type": "org.apache.tomcat.util.net.AbstractEndpoint", "methods": [{"name": "setBindOnInit", "parameterTypes": ["boolean"]}]}, {"type": "org.apache.tomcat.util.net.NioEndpoint"}, {"type": "org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper"}, {"type": "org.apache.tomcat.util.net.NioEndpoint$SocketProcessor"}, {"type": "org.apache.tomcat.util.net.SocketProcessorBase"}, {"type": "org.apache.tomcat.util.threads.TaskThread$WrappingRunnable"}, {"type": "org.apache.tomcat.util.threads.ThreadPoolExecutor"}, {"type": "org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker"}, {"type": "org.apache.tomcat.websocket.server.WsFilter"}, {"type": "org.apache.tomcat.websocket.server.WsSci"}, {"type": "org.aspectj.lang.ProceedingJoinPoint"}, {"type": "org.aspectj.weaver.Advice"}, {"type": "org.cache2k.Cache2kBuilder"}, {"type": "org.crac.Core"}, {"type": "org.crac.Resource"}, {"type": "org.eclipse.core.runtime.FileLocator"}, {"type": "org.eclipse.jetty.client.HttpClient"}, {"type": "org.eclipse.jetty.ee10.webapp.WebAppContext"}, {"type": "org.eclipse.jetty.ee10.websocket.jakarta.server.config.JakartaWebSocketServletContainerInitializer"}, {"type": "org.eclipse.jetty.server.Server"}, {"type": "org.eclipse.jetty.util.Loader"}, {"type": "org.elasticsearch.client.RestClientBuilder"}, {"type": "org.flywaydb.core.Flyway", "allDeclaredFields": true}, {"type": "org.flywaydb.core.api.migration.baseline.BaselineAppliedMigration"}, {"type": "org.flywaydb.core.api.migration.baseline.BaselineMigrationConfigurationExtension", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "getBaselineMigrationPrefix", "parameterTypes": []}, {"name": "setBaselineMigrationPrefix", "parameterTypes": ["java.lang.String"]}]}, {"type": "org.flywaydb.core.api.migration.baseline.BaselineMigrationResolver"}, {"type": "org.flywaydb.core.api.migration.baseline.BaselineMigrationTypeResolver"}, {"type": "org.flywaydb.core.api.migration.baseline.BaselineResourceTypeProvider"}, {"type": "org.flywaydb.core.experimental.migration.CoreMigrationTypeResolver"}, {"type": "org.flywaydb.core.extensibility.ConfigurationExtension"}, {"type": "org.flywaydb.core.extensibility.Plugin"}, {"type": "org.flywaydb.core.internal.command.clean.CleanModeConfigurationExtension", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "getClean", "parameterTypes": []}, {"name": "setClean", "parameterTypes": ["org.flywaydb.core.internal.command.clean.CleanModel"]}]}, {"type": "org.flywaydb.core.internal.command.clean.CleanModel", "allDeclaredFields": true}, {"type": "org.flywaydb.core.internal.command.clean.SchemaModel", "allDeclaredFields": true}, {"type": "org.flywaydb.core.internal.configuration.extensions.DeployScriptFilenameConfigurationExtension", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "getScriptFilename", "parameterTypes": []}, {"name": "setScriptFilename", "parameterTypes": ["java.lang.String"]}]}, {"type": "org.flywaydb.core.internal.configuration.extensions.PrepareScriptFilenameConfigurationExtension", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "getScriptFilename", "parameterTypes": []}, {"name": "setScriptFilename", "parameterTypes": ["java.lang.String"]}]}, {"type": "org.flywaydb.core.internal.configuration.resolvers.EnvironmentProvisionerNone"}, {"type": "org.flywaydb.core.internal.configuration.resolvers.EnvironmentVariableResolver"}, {"type": "org.flywaydb.core.internal.database.base.TestContainersDatabaseType"}, {"type": "org.flywaydb.core.internal.database.h2.H2DatabaseType"}, {"type": "org.flywaydb.core.internal.database.sqlite.SQLiteDatabaseType"}, {"type": "org.flywaydb.core.internal.logging.slf4j.Slf4jLogCreator", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.flywaydb.core.internal.proprietaryStubs.AuthCommandExtensionStub"}, {"type": "org.flywaydb.core.internal.proprietaryStubs.CommandExtensionStub"}, {"type": "org.flywaydb.core.internal.proprietaryStubs.LicensingConfigurationExtensionStub", "allDeclaredFields": true}, {"type": "org.flywaydb.core.internal.proprietaryStubs.PATTokenConfigurationExtensionStub", "allDeclaredFields": true}, {"type": "org.flywaydb.core.internal.publishing.PublishingConfigurationExtension", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "isCheckDriftOnMigrate", "parameterTypes": []}, {"name": "isPublishResult", "parameterTypes": []}, {"name": "setCheckDriftOnMigrate", "parameterTypes": ["boolean"]}, {"name": "setPublishResult", "parameterTypes": ["boolean"]}]}, {"type": "org.flywaydb.core.internal.resource.CoreResourceTypeProvider"}, {"type": "org.flywaydb.core.internal.schemahistory.BaseAppliedMigration"}, {"type": "org.flywaydb.database.cockroachdb.CockroachDBDatabaseType"}, {"type": "org.flywaydb.database.oracle.OracleConfigurationExtension"}, {"type": "org.flywaydb.database.postgresql.PostgreSQLConfigurationExtension", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "getTransactional", "parameterTypes": []}, {"name": "isTransactionalLock", "parameterTypes": []}, {"name": "setTransactional", "parameterTypes": ["org.flywaydb.database.postgresql.TransactionalModel"]}, {"name": "setTransactionalLock", "parameterTypes": ["boolean"]}]}, {"type": "org.flywaydb.database.postgresql.PostgreSQLDatabaseType"}, {"type": "org.flywaydb.database.postgresql.TransactionalModel", "allDeclaredFields": true}, {"type": "org.flywaydb.database.sqlserver.SQLServerConfigurationExtension"}, {"type": "org.glassfish.jersey.server.spring.SpringComponentProvider"}, {"type": "org.glassfish.json.JsonProviderImpl", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.graalvm.nativeimage.ImageInfo", "methods": [{"name": "inImageCode", "parameterTypes": []}]}, {"type": "org.h2.Driver"}, {"type": "org.h2.server.web.JakartaWebServlet"}, {"type": "org.hsqldb.jdbc.JDBCDriver"}, {"type": "org.infinispan.spring.embedded.provider.SpringEmbeddedCacheManager"}, {"type": "org.jboss.logging.Logger"}, {"type": "org.jooq.DSLContext"}, {"type": "org.neo4j.driver.Driver"}, {"type": "org.postgresql.Driver", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.postgresql.core.QueryExecutorCloseAction", "fields": [{"name": "pgStream"}]}, {"type": "org.postgresql.jdbc.PgStatement", "fields": [{"name": "cancelTimerTask"}, {"name": "isClosed"}, {"name": "statementState"}]}, {"type": "org.quartz.Scheduler"}, {"type": "org.quartz.core.QuartzScheduler"}, {"type": "org.reactivestreams.Publisher"}, {"type": "org.slf4j.<PERSON>"}, {"type": "org.slf4j.MDC"}, {"type": "org.slf4j.bridge.SLF4JBridgeHandler"}, {"type": "org.slf4j.spi.LocationAwareLogger"}, {"type": "org.springframework.amqp.core.AmqpAdmin"}, {"type": "org.springframework.amqp.core.AmqpTemplate"}, {"type": "org.springframework.amqp.core.MessageListener"}, {"type": "org.springframework.amqp.rabbit.annotation.EnableRabbit"}, {"type": "org.springframework.amqp.rabbit.annotation.MultiRabbitBootstrapConfiguration", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.amqp.rabbit.annotation.RabbitBootstrapConfiguration", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.amqp.rabbit.annotation.RabbitListenerAnnotationBeanPostProcessor", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.amqp.rabbit.annotation.RabbitListenerConfigurationSelector", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.amqp.rabbit.config.AbstractRabbitListenerContainerFactory", "allDeclaredFields": true}, {"type": "org.springframework.amqp.rabbit.config.BaseRabbitListenerContainerFactory", "allDeclaredFields": true}, {"type": "org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory", "allDeclaredFields": true}, {"type": "org.springframework.amqp.rabbit.connection.AbstractConnectionFactory", "allDeclaredFields": true}, {"type": "org.springframework.amqp.rabbit.connection.CachingConnectionFactory", "allDeclaredFields": true}, {"type": "org.springframework.amqp.rabbit.connection.ConnectionFactory"}, {"type": "org.springframework.amqp.rabbit.connection.PublisherCallbackChannel$Listener"}, {"type": "org.springframework.amqp.rabbit.connection.RabbitAccessor", "allDeclaredFields": true}, {"type": "org.springframework.amqp.rabbit.core.RabbitAdmin", "allDeclaredFields": true}, {"type": "org.springframework.amqp.rabbit.core.RabbitMessageOperations"}, {"type": "org.springframework.amqp.rabbit.core.RabbitMessagingTemplate", "allDeclaredFields": true}, {"type": "org.springframework.amqp.rabbit.core.RabbitOperations"}, {"type": "org.springframework.amqp.rabbit.core.RabbitTemplate", "allDeclaredFields": true}, {"type": "org.springframework.amqp.rabbit.listener.RabbitListenerContainerFactory"}, {"type": "org.springframework.amqp.rabbit.listener.RabbitListenerEndpointRegistry", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener"}, {"type": "org.springframework.amqp.rabbit.support.ListenerContainerAware"}, {"type": "org.springframework.aop.Advisor"}, {"type": "org.springframework.aop.PointcutAdvisor"}, {"type": "org.springframework.aop.framework.AbstractAdvisingBeanPostProcessor", "allDeclaredFields": true}, {"type": "org.springframework.aop.framework.AopInfrastructureBean"}, {"type": "org.springframework.aop.framework.ProxyConfig", "allDeclaredFields": true, "methods": [{"name": "setProxyTargetClass", "parameterTypes": ["boolean"]}]}, {"type": "org.springframework.aop.framework.ProxyProcessorSupport", "allDeclaredFields": true, "methods": [{"name": "setOrder", "parameterTypes": ["int"]}]}, {"type": "org.springframework.aop.framework.autoproxy.AbstractAdvisorAutoProxyCreator", "allDeclaredFields": true}, {"type": "org.springframework.aop.framework.autoproxy.AbstractAutoProxyCreator", "allDeclaredFields": true}, {"type": "org.springframework.aop.framework.autoproxy.AbstractBeanFactoryAwareAdvisingPostProcessor", "allDeclaredFields": true}, {"type": "org.springframework.aop.framework.autoproxy.InfrastructureAdvisorAutoProxyCreator", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.aop.scope.ScopedObject"}, {"type": "org.springframework.aop.support.AbstractBeanFactoryPointcutAdvisor", "allDeclaredFields": true}, {"type": "org.springframework.aop.support.AbstractPointcutAdvisor", "allDeclaredFields": true}, {"type": "org.springframework.aot.generate.Generated"}, {"type": "org.springframework.aot.hint.annotation.Reflective"}, {"type": "org.springframework.batch.core.launch.JobLauncher"}, {"type": "org.springframework.beans.factory.Aware"}, {"type": "org.springframework.beans.factory.BeanClassLoaderAware"}, {"type": "org.springframework.beans.factory.BeanFactoryAware"}, {"type": "org.springframework.beans.factory.BeanNameAware"}, {"type": "org.springframework.beans.factory.DisposableBean"}, {"type": "org.springframework.beans.factory.FactoryBean"}, {"type": "org.springframework.beans.factory.InitializingBean"}, {"type": "org.springframework.beans.factory.SmartInitializingSingleton"}, {"type": "org.springframework.beans.factory.annotation.Autowired"}, {"type": "org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.beans.factory.annotation.Qualifier"}, {"type": "org.springframework.beans.factory.annotation.Value"}, {"type": "org.springframework.beans.factory.aot.BeanFactoryInitializationAotProcessor"}, {"type": "org.springframework.beans.factory.aot.BeanRegistrationAotProcessor"}, {"type": "org.springframework.beans.factory.config.BeanFactoryPostProcessor"}, {"type": "org.springframework.beans.factory.config.BeanPostProcessor"}, {"type": "org.springframework.beans.factory.config.InstantiationAwareBeanPostProcessor"}, {"type": "org.springframework.beans.factory.config.SmartInstantiationAwareBeanPostProcessor"}, {"type": "org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory"}, {"type": "org.springframework.beans.factory.support.AbstractBeanFactory"}, {"type": "org.springframework.beans.factory.support.BeanDefinitionRegistryPostProcessor"}, {"type": "org.springframework.beans.factory.support.ConstructorResolver"}, {"type": "org.springframework.beans.factory.support.DefaultSingletonBeanRegistry"}, {"type": "org.springframework.beans.factory.support.NullBean", "allDeclaredFields": true}, {"type": "org.springframework.beans.factory.support.SimpleInstantiationStrategy"}, {"type": "org.springframework.boot.ApplicationProperties", "allDeclaredFields": true, "methods": [{"name": "setAllowCircularReferences", "parameterTypes": ["boolean"]}]}, {"type": "org.springframework.boot.ClearCachesApplicationListener", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.SpringApplication"}, {"type": "org.springframework.boot.SpringBootConfiguration"}, {"type": "org.springframework.boot.ansi.AnsiOutput$Enabled"}, {"type": "org.springframework.boot.autoconfigure.AutoConfiguration"}, {"type": "org.springframework.boot.autoconfigure.AutoConfigurationExcludeFilter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.AutoConfigurationImportSelector", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.AutoConfigurationImportSelector$AutoConfigurationGroup", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.AutoConfigurationPackage"}, {"type": "org.springframework.boot.autoconfigure.AutoConfigurationPackages$BasePackages", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": ["java.lang.String[]"]}]}, {"type": "org.springframework.boot.autoconfigure.AutoConfigurationPackages$Registrar", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.AutoConfigureAfter"}, {"type": "org.springframework.boot.autoconfigure.AutoConfigureBefore"}, {"type": "org.springframework.boot.autoconfigure.AutoConfigureOrder"}, {"type": "org.springframework.boot.autoconfigure.BackgroundPreinitializer", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.EnableAutoConfiguration"}, {"type": "org.springframework.boot.autoconfigure.SharedMetadataReaderFactoryContextInitializer", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.SpringBootApplication"}, {"type": "org.springframework.boot.autoconfigure.amqp.AbstractConnectionFactoryConfigurer", "allDeclaredFields": true}, {"type": "org.springframework.boot.autoconfigure.amqp.AbstractRabbitListenerContainerFactoryConfigurer", "allDeclaredFields": true}, {"type": "org.springframework.boot.autoconfigure.amqp.CachingConnectionFactoryConfigurer", "allDeclaredFields": true}, {"type": "org.springframework.boot.autoconfigure.amqp.DirectRabbitListenerContainerFactoryConfigurer", "allDeclaredFields": true}, {"type": "org.springframework.boot.autoconfigure.amqp.PropertiesRabbitConnectionDetails", "allDeclaredFields": true}, {"type": "org.springframework.boot.autoconfigure.amqp.RabbitAnnotationDrivenConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.beans.factory.ObjectProvider", "org.springframework.beans.factory.ObjectProvider", "org.springframework.beans.factory.ObjectProvider", "org.springframework.boot.autoconfigure.amqp.RabbitProperties"]}, {"name": "directRabbitListenerContainerFactoryConfigurer", "parameterTypes": []}, {"name": "simpleRabbitListenerContainerFactory", "parameterTypes": ["org.springframework.boot.autoconfigure.amqp.SimpleRabbitListenerContainerFactoryConfigurer", "org.springframework.amqp.rabbit.connection.ConnectionFactory", "org.springframework.beans.factory.ObjectProvider"]}, {"name": "simpleRabbitListenerContainerFactoryConfigurer", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.amqp.RabbitAnnotationDrivenConfiguration$EnableRabbitConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.amqp.RabbitAutoConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.amqp.RabbitAutoConfiguration$MessagingTemplateConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "rabbitMessagingTemplate", "parameterTypes": ["org.springframework.amqp.rabbit.core.RabbitTemplate"]}]}, {"type": "org.springframework.boot.autoconfigure.amqp.RabbitAutoConfiguration$RabbitConnectionFactoryCreator", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.boot.autoconfigure.amqp.RabbitProperties"]}, {"name": "rabbitConnectionDetails", "parameterTypes": []}, {"name": "rabbitConnectionFactoryBeanConfigurer", "parameterTypes": ["org.springframework.core.io.ResourceLoader", "org.springframework.boot.autoconfigure.amqp.RabbitConnectionDetails", "org.springframework.beans.factory.ObjectProvider", "org.springframework.beans.factory.ObjectProvider", "org.springframework.beans.factory.ObjectProvider"]}, {"name": "rabbitConnectionFactoryConfigurer", "parameterTypes": ["org.springframework.boot.autoconfigure.amqp.RabbitConnectionDetails", "org.springframework.beans.factory.ObjectProvider"]}]}, {"type": "org.springframework.boot.autoconfigure.amqp.RabbitAutoConfiguration$RabbitTemplateConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "rabbitTemplateConfigurer", "parameterTypes": ["org.springframework.boot.autoconfigure.amqp.RabbitProperties", "org.springframework.beans.factory.ObjectProvider", "org.springframework.beans.factory.ObjectProvider"]}]}, {"type": "org.springframework.boot.autoconfigure.amqp.RabbitConnectionDetails"}, {"type": "org.springframework.boot.autoconfigure.amqp.RabbitConnectionFactoryBeanConfigurer", "allDeclaredFields": true}, {"type": "org.springframework.boot.autoconfigure.amqp.RabbitProperties", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.amqp.RabbitStreamConfiguration"}, {"type": "org.springframework.boot.autoconfigure.amqp.RabbitTemplateConfigurer", "allDeclaredFields": true}, {"type": "org.springframework.boot.autoconfigure.amqp.SimpleRabbitListenerContainerFactoryConfigurer", "allDeclaredFields": true}, {"type": "org.springframework.boot.autoconfigure.aop.AopAutoConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.aop.AopAutoConfiguration$ClassProxyingConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "forceAutoProxyCreatorToUseClassProxying", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.availability.ApplicationAvailabilityAutoConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "applicationAvailability", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.batch.JobRepositoryDependsOnDatabaseInitializationDetector", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration$CacheConfigurationImportSelector", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.cache.CacheCondition", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.cache.CacheType"}, {"type": "org.springframework.boot.autoconfigure.condition.ConditionEvaluationReportAutoConfigurationImportListener", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.condition.ConditionalOnBean"}, {"type": "org.springframework.boot.autoconfigure.condition.ConditionalOnCheckpointRestore"}, {"type": "org.springframework.boot.autoconfigure.condition.ConditionalOnClass"}, {"type": "org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean"}, {"type": "org.springframework.boot.autoconfigure.condition.ConditionalOnMissingClass"}, {"type": "org.springframework.boot.autoconfigure.condition.ConditionalOnNotWarDeployment"}, {"type": "org.springframework.boot.autoconfigure.condition.ConditionalOnProperty"}, {"type": "org.springframework.boot.autoconfigure.condition.ConditionalOnResource"}, {"type": "org.springframework.boot.autoconfigure.condition.ConditionalOnSingleCandidate"}, {"type": "org.springframework.boot.autoconfigure.condition.ConditionalOnThreading"}, {"type": "org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication"}, {"type": "org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication$Type"}, {"type": "org.springframework.boot.autoconfigure.condition.OnBeanCondition", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.condition.OnClassCondition", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.condition.OnPropertyCondition", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.condition.OnResourceCondition", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.condition.OnThreadingCondition", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.condition.OnWarDeploymentCondition", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.condition.OnWebApplicationCondition", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.condition.SearchStrategy"}, {"type": "org.springframework.boot.autoconfigure.context.ConfigurationPropertiesAutoConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.context.LifecycleAutoConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "defaultLifecycleProcessor", "parameterTypes": ["org.springframework.boot.autoconfigure.context.LifecycleProperties"]}]}, {"type": "org.springframework.boot.autoconfigure.context.LifecycleProperties", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.context.MessageSourceAutoConfiguration$ResourceBundleCondition", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.context.PropertyPlaceholderAutoConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "propertySourcesPlaceholderConfigurer", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.dao.PersistenceExceptionTranslationAutoConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "persistenceExceptionTranslationPostProcessor", "parameterTypes": ["org.springframework.core.env.Environment"]}]}, {"type": "org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "flywayDefaultDdlModeProvider", "parameterTypes": ["org.springframework.beans.factory.ObjectProvider"]}, {"name": "stringOrNumberMigrationVersionConverter", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration$FlywayAutoConfigurationRuntimeHints"}, {"type": "org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration$FlywayConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.boot.autoconfigure.flyway.FlywayProperties"]}, {"name": "flyway", "parameterTypes": ["org.springframework.boot.autoconfigure.flyway.FlywayConnectionDetails", "org.springframework.core.io.ResourceLoader", "org.springframework.beans.factory.ObjectProvider", "org.springframework.beans.factory.ObjectProvider", "org.springframework.beans.factory.ObjectProvider", "org.springframework.beans.factory.ObjectProvider", "org.springframework.beans.factory.ObjectProvider", "org.springframework.boot.autoconfigure.flyway.ResourceProviderCustomizer"]}, {"name": "flywayConnectionDetails", "parameterTypes": []}, {"name": "flywayInitializer", "parameterTypes": ["org.flywaydb.core.Flyway", "org.springframework.beans.factory.ObjectProvider"]}, {"name": "postgresqlFlywayConfigurationCustomizer", "parameterTypes": []}, {"name": "resourceProviderCustomizer", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration$FlywayDataSourceCondition", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration$PostgresqlFlywayConfigurationCustomizer", "allDeclaredFields": true}, {"type": "org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration$PropertiesFlywayConnectionDetails", "allDeclaredFields": true}, {"type": "org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration$StringOrNumberToMigrationVersionConverter", "allDeclaredFields": true}, {"type": "org.springframework.boot.autoconfigure.flyway.FlywayConfigurationCustomizer"}, {"type": "org.springframework.boot.autoconfigure.flyway.FlywayConnectionDetails"}, {"type": "org.springframework.boot.autoconfigure.flyway.FlywayDataSource"}, {"type": "org.springframework.boot.autoconfigure.flyway.FlywayMigrationInitializer", "allDeclaredFields": true}, {"type": "org.springframework.boot.autoconfigure.flyway.FlywayMigrationInitializerDatabaseInitializerDetector", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.flyway.FlywayProperties", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "setEnabled", "parameterTypes": ["boolean"]}]}, {"type": "org.springframework.boot.autoconfigure.flyway.FlywaySchemaManagementProvider", "allDeclaredFields": true}, {"type": "org.springframework.boot.autoconfigure.flyway.ResourceProviderCustomizer", "allDeclaredFields": true}, {"type": "org.springframework.boot.autoconfigure.freemarker.FreeMarkerTemplateAvailabilityProvider", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.groovy.template.GroovyTemplateAvailabilityProvider", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.gson.GsonAutoConfiguration"}, {"type": "org.springframework.boot.autoconfigure.http.GsonHttpMessageConvertersConfiguration"}, {"type": "org.springframework.boot.autoconfigure.http.HttpMessageConverters", "allDeclaredFields": true}, {"type": "org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "messageConverters", "parameterTypes": ["org.springframework.beans.factory.ObjectProvider"]}]}, {"type": "org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration$HttpMessageConvertersAutoConfigurationRuntimeHints"}, {"type": "org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration$NotReactiveWebApplicationCondition", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration$StringHttpMessageConverterConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "stringHttpMessageConverter", "parameterTypes": ["org.springframework.core.env.Environment"]}]}, {"type": "org.springframework.boot.autoconfigure.http.JacksonHttpMessageConvertersConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.http.JacksonHttpMessageConvertersConfiguration$MappingJackson2HttpMessageConverterConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "mappingJackson2HttpMessageConverter", "parameterTypes": ["com.fasterxml.jackson.databind.ObjectMapper"]}]}, {"type": "org.springframework.boot.autoconfigure.http.JsonbHttpMessageConvertersConfiguration"}, {"type": "org.springframework.boot.autoconfigure.http.client.HttpClientAutoConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "clientHttpRequestFactoryBuilder", "parameterTypes": ["org.springframework.boot.autoconfigure.http.client.HttpClientProperties"]}, {"name": "clientHttpRequestFactorySettings", "parameterTypes": ["org.springframework.boot.autoconfigure.http.client.HttpClientProperties", "org.springframework.beans.factory.ObjectProvider"]}]}, {"type": "org.springframework.boot.autoconfigure.http.client.HttpClientProperties", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.http.client.NotReactiveWebApplicationCondition", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.boot.autoconfigure.info.ProjectInfoProperties"]}, {"name": "buildProperties", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration$GitResourceAvailableCondition", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.info.ProjectInfoProperties", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.integration.IntegrationPropertiesEnvironmentPostProcessor", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer"}, {"type": "org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "jsonComponentModule", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "standardJacksonObjectMapperBuilderCustomizer", "parameterTypes": ["org.springframework.boot.autoconfigure.jackson.JacksonProperties", "org.springframework.beans.factory.ObjectProvider"]}]}, {"type": "org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration$StandardJackson2ObjectMapperBuilderCustomizer", "allDeclaredFields": true}, {"type": "org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonMixinConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "jsonMixinModule", "parameterTypes": ["org.springframework.context.ApplicationContext", "org.springframework.boot.jackson.JsonMixinModuleEntries"]}, {"name": "jsonMixinModuleEntries", "parameterTypes": ["org.springframework.context.ApplicationContext"]}]}, {"type": "org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "jacksonObjectMapperBuilder", "parameterTypes": ["org.springframework.context.ApplicationContext", "java.util.List"]}]}, {"type": "org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "jacksonObjectMapper", "parameterTypes": ["org.springframework.http.converter.json.Jackson2ObjectMapperBuilder"]}]}, {"type": "org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "parameterNamesModule", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.jackson.JacksonProperties", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration$EmbeddedDatabaseCondition", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration$PooledDataSourceAvailableCondition", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration$PooledDataSourceCondition", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.jdbc.DataSourceCheckpointRestoreConfiguration"}, {"type": "org.springframework.boot.autoconfigure.jdbc.DataSourceProperties", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "setDriverClassName", "parameterTypes": ["java.lang.String"]}, {"name": "setPassword", "parameterTypes": ["java.lang.String"]}, {"name": "setUrl", "parameterTypes": ["java.lang.String"]}, {"name": "setUsername", "parameterTypes": ["java.lang.String"]}]}, {"type": "org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration$JdbcTransactionManagerConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "transactionManager", "parameterTypes": ["org.springframework.core.env.Environment", "javax.sql.DataSource", "org.springframework.beans.factory.ObjectProvider"]}]}, {"type": "org.springframework.boot.autoconfigure.jdbc.JdbcClientAutoConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "jdbcClient", "parameterTypes": ["org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate"]}]}, {"type": "org.springframework.boot.autoconfigure.jdbc.JdbcConnectionDetails"}, {"type": "org.springframework.boot.autoconfigure.jdbc.JdbcProperties", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.jdbc.JdbcTemplateConfiguration"}, {"type": "org.springframework.boot.autoconfigure.jdbc.NamedParameterJdbcTemplateConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "namedParameterJdbcTemplate", "parameterTypes": ["org.springframework.jdbc.core.JdbcTemplate"]}]}, {"type": "org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration$HikariPoolDataSourceMetadataProviderConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "hikariPoolDataSourceMetadataProvider", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.jsonb.JsonbAutoConfiguration"}, {"type": "org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.mustache.MustacheTemplateAvailabilityProvider", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration"}, {"type": "org.springframework.boot.autoconfigure.quartz.SchedulerDependsOnDatabaseInitializationDetector", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.security.ConditionalOnDefaultWebSecurity"}, {"type": "org.springframework.boot.autoconfigure.security.DefaultWebSecurityCondition", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.security.SecurityDataConfiguration"}, {"type": "org.springframework.boot.autoconfigure.security.SecurityProperties", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.security.reactive.ReactiveUserDetailsServiceAutoConfiguration$MissingAlternativeOrUserPropertiesConfigured", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.security.reactive.ReactiveUserDetailsServiceAutoConfiguration$RSocketEnabledOrReactiveWebApplication", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "authenticationEventPublisher", "parameterTypes": ["org.springframework.context.ApplicationEventPublisher"]}]}, {"type": "org.springframework.boot.autoconfigure.security.servlet.SecurityFilterAutoConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "securityFilterChainRegistration", "parameterTypes": ["org.springframework.boot.autoconfigure.security.SecurityProperties"]}]}, {"type": "org.springframework.boot.autoconfigure.security.servlet.SpringBootWebSecurityConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.security.servlet.UserDetailsServiceAutoConfiguration"}, {"type": "org.springframework.boot.autoconfigure.service.connection.ConnectionDetails"}, {"type": "org.springframework.boot.autoconfigure.session.JdbcIndexedSessionRepositoryDependsOnDatabaseInitializationDetector", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.sql.init.DataSourceInitializationConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "dataSourceScriptDatabaseInitializer", "parameterTypes": ["javax.sql.DataSource", "org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties"]}]}, {"type": "org.springframework.boot.autoconfigure.sql.init.R2dbcInitializationConfiguration"}, {"type": "org.springframework.boot.autoconfigure.sql.init.SqlDataSourceScriptDatabaseInitializer", "allDeclaredFields": true}, {"type": "org.springframework.boot.autoconfigure.sql.init.SqlInitializationAutoConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.sql.init.SqlInitializationAutoConfiguration$SqlInitializationModeCondition", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.sql.init.SqlR2dbcScriptDatabaseInitializer"}, {"type": "org.springframework.boot.autoconfigure.ssl.FileWatcher", "allDeclaredFields": true}, {"type": "org.springframework.boot.autoconfigure.ssl.SslAutoConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.core.io.ResourceLoader", "org.springframework.boot.autoconfigure.ssl.SslProperties"]}, {"name": "fileWatcher", "parameterTypes": []}, {"name": "sslBundleRegistry", "parameterTypes": ["org.springframework.beans.factory.ObjectProvider"]}, {"name": "sslPropertiesSslBundleRegistrar", "parameterTypes": ["org.springframework.boot.autoconfigure.ssl.FileWatcher"]}]}, {"type": "org.springframework.boot.autoconfigure.ssl.SslBundleRegistrar"}, {"type": "org.springframework.boot.autoconfigure.ssl.SslProperties", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.ssl.SslPropertiesBundleRegistrar", "allDeclaredFields": true}, {"type": "org.springframework.boot.autoconfigure.task.TaskExecutionAutoConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.task.TaskExecutionProperties", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.task.TaskExecutorConfigurations$SimpleAsyncTaskExecutorBuilderConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.boot.autoconfigure.task.TaskExecutionProperties", "org.springframework.beans.factory.ObjectProvider", "org.springframework.beans.factory.ObjectProvider"]}, {"name": "simpleAsyncTaskExecutorBuilder", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.task.TaskExecutorConfigurations$TaskExecutorConfiguration"}, {"type": "org.springframework.boot.autoconfigure.task.TaskExecutorConfigurations$ThreadPoolTaskExecutorBuilderConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "threadPoolTaskExecutorBuilder", "parameterTypes": ["org.springframework.boot.autoconfigure.task.TaskExecutionProperties", "org.springframework.beans.factory.ObjectProvider", "org.springframework.beans.factory.ObjectProvider"]}]}, {"type": "org.springframework.boot.autoconfigure.task.TaskSchedulingAutoConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.task.TaskSchedulingConfigurations$SimpleAsyncTaskSchedulerBuilderConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.boot.autoconfigure.task.TaskSchedulingProperties", "org.springframework.beans.factory.ObjectProvider"]}, {"name": "simpleAsyncTaskSchedulerBuilder", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.task.TaskSchedulingConfigurations$TaskSchedulerConfiguration"}, {"type": "org.springframework.boot.autoconfigure.task.TaskSchedulingConfigurations$ThreadPoolTaskSchedulerBuilderConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "threadPoolTaskSchedulerBuilder", "parameterTypes": ["org.springframework.boot.autoconfigure.task.TaskSchedulingProperties", "org.springframework.beans.factory.ObjectProvider"]}]}, {"type": "org.springframework.boot.autoconfigure.task.TaskSchedulingProperties", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.thread.Threading"}, {"type": "org.springframework.boot.autoconfigure.thymeleaf.ThymeleafTemplateAvailabilityProvider", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.transaction.ExecutionListenersTransactionManagerCustomizer", "allDeclaredFields": true}, {"type": "org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$EnableTransactionManagementConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$EnableTransactionManagementConfiguration$CglibAutoProxyConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$TransactionTemplateConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "transactionTemplate", "parameterTypes": ["org.springframework.transaction.PlatformTransactionManager"]}]}, {"type": "org.springframework.boot.autoconfigure.transaction.TransactionManagerCustomizationAutoConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "platformTransactionManagerCustomizers", "parameterTypes": ["org.springframework.beans.factory.ObjectProvider"]}, {"name": "transactionExecutionListeners", "parameterTypes": ["org.springframework.beans.factory.ObjectProvider"]}]}, {"type": "org.springframework.boot.autoconfigure.transaction.TransactionManagerCustomizer"}, {"type": "org.springframework.boot.autoconfigure.transaction.TransactionManagerCustomizers", "allDeclaredFields": true}, {"type": "org.springframework.boot.autoconfigure.transaction.TransactionProperties", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.validation.ValidationAutoConfiguration"}, {"type": "org.springframework.boot.autoconfigure.web.ConditionalOnEnabledResourceChain"}, {"type": "org.springframework.boot.autoconfigure.web.OnEnabledResourceChainCondition", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.web.ServerProperties", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "setPort", "parameterTypes": ["java.lang.Integer"]}]}, {"type": "org.springframework.boot.autoconfigure.web.WebProperties", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.web.WebResourcesRuntimeHints"}, {"type": "org.springframework.boot.autoconfigure.web.client.AutoConfiguredRestClientSsl", "allDeclaredFields": true}, {"type": "org.springframework.boot.autoconfigure.web.client.HttpMessageConvertersRestClientCustomizer", "allDeclaredFields": true}, {"type": "org.springframework.boot.autoconfigure.web.client.NotReactiveWebApplicationCondition", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.web.client.RestClientAutoConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "httpMessageConvertersRestClientCustomizer", "parameterTypes": ["org.springframework.beans.factory.ObjectProvider"]}, {"name": "restClientBuilderConfigurer", "parameterTypes": ["org.springframework.beans.factory.ObjectProvider", "org.springframework.beans.factory.ObjectProvider", "org.springframework.beans.factory.ObjectProvider"]}, {"name": "restClientSsl", "parameterTypes": ["org.springframework.beans.factory.ObjectProvider", "org.springframework.boot.ssl.SslBundles"]}]}, {"type": "org.springframework.boot.autoconfigure.web.client.RestClientBuilderConfigurer", "allDeclaredFields": true}, {"type": "org.springframework.boot.autoconfigure.web.client.RestClientSsl"}, {"type": "org.springframework.boot.autoconfigure.web.client.RestTemplateAutoConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.web.embedded.EmbeddedWebServerFactoryCustomizerAutoConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.web.embedded.EmbeddedWebServerFactoryCustomizerAutoConfiguration$TomcatWebServerFactoryCustomizerConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "tomcatWebServerFactoryCustomizer", "parameterTypes": ["org.springframework.core.env.Environment", "org.springframework.boot.autoconfigure.web.ServerProperties"]}]}, {"type": "org.springframework.boot.autoconfigure.web.embedded.TomcatWebServerFactoryCustomizer", "allDeclaredFields": true}, {"type": "org.springframework.boot.autoconfigure.web.format.WebConversionService", "allDeclaredFields": true}, {"type": "org.springframework.boot.autoconfigure.web.servlet.ConditionalOnMissingFilterBean"}, {"type": "org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DefaultDispatcherServletCondition", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "dispatcherServlet", "parameterTypes": ["org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties"]}]}, {"type": "org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletRegistrationCondition", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletRegistrationConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "dispatcherServletRegistration", "parameterTypes": ["org.springframework.web.servlet.DispatcherServlet", "org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties", "org.springframework.beans.factory.ObjectProvider"]}]}, {"type": "org.springframework.boot.autoconfigure.web.servlet.DispatcherServletPath"}, {"type": "org.springframework.boot.autoconfigure.web.servlet.DispatcherServletRegistrationBean", "allDeclaredFields": true}, {"type": "org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.boot.autoconfigure.web.ServerProperties"]}, {"name": "characterE<PERSON><PERSON><PERSON><PERSON>er", "parameterTypes": []}, {"name": "localeCharsetMappingsCustomizer", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration$LocaleCharsetMappingsCustomizer", "allDeclaredFields": true}, {"type": "org.springframework.boot.autoconfigure.web.servlet.JspTemplateAvailabilityProvider", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.boot.autoconfigure.web.servlet.MultipartProperties"]}, {"name": "multipartConfigElement", "parameterTypes": []}, {"name": "multipartResolver", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.web.servlet.MultipartProperties", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryAutoConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "servletWebServerFactoryCustomizer", "parameterTypes": ["org.springframework.boot.autoconfigure.web.ServerProperties", "org.springframework.beans.factory.ObjectProvider", "org.springframework.beans.factory.ObjectProvider", "org.springframework.beans.factory.ObjectProvider"]}, {"name": "tomcatServletWebServerFactoryCustomizer", "parameterTypes": ["org.springframework.boot.autoconfigure.web.ServerProperties"]}]}, {"type": "org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryAutoConfiguration$BeanPostProcessorsRegistrar", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryConfiguration$EmbeddedJetty"}, {"type": "org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryConfiguration$EmbeddedTomcat", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "tomcatServletWebServerFactory", "parameterTypes": ["org.springframework.beans.factory.ObjectProvider", "org.springframework.beans.factory.ObjectProvider", "org.springframework.beans.factory.ObjectProvider"]}]}, {"type": "org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryConfiguration$EmbeddedUndertow"}, {"type": "org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryCustomizer", "allDeclaredFields": true}, {"type": "org.springframework.boot.autoconfigure.web.servlet.TomcatServletWebServerFactoryCustomizer", "allDeclaredFields": true}, {"type": "org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "formContentFilter", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties", "org.springframework.boot.autoconfigure.web.WebProperties", "org.springframework.beans.factory.ObjectProvider", "org.springframework.beans.factory.ObjectProvider", "org.springframework.beans.factory.ListableBeanFactory"]}, {"name": "flashMapManager", "parameterTypes": []}, {"name": "localeResolver", "parameterTypes": []}, {"name": "mvcContentNegotiationManager", "parameterTypes": []}, {"name": "mvcConversionService", "parameterTypes": []}, {"name": "mvcValidator", "parameterTypes": []}, {"name": "themeResolver", "parameterTypes": []}, {"name": "viewNameTranslator", "parameterTypes": []}, {"name": "welcomePageHandlerMapping", "parameterTypes": ["org.springframework.context.ApplicationContext", "org.springframework.format.support.FormattingConversionService", "org.springframework.web.servlet.resource.ResourceUrlProvider"]}, {"name": "welcomePageNotAcceptableHandlerMapping", "parameterTypes": ["org.springframework.context.ApplicationContext", "org.springframework.format.support.FormattingConversionService", "org.springframework.web.servlet.resource.ResourceUrlProvider"]}]}, {"type": "org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.boot.autoconfigure.web.WebProperties", "org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties", "org.springframework.beans.factory.ListableBeanFactory", "org.springframework.beans.factory.ObjectProvider", "org.springframework.beans.factory.ObjectProvider", "org.springframework.beans.factory.ObjectProvider", "org.springframework.beans.factory.ObjectProvider"]}, {"name": "defaultViewResolver", "parameterTypes": []}, {"name": "requestContextFilter", "parameterTypes": []}, {"name": "viewResolver", "parameterTypes": ["org.springframework.beans.factory.BeanFactory"]}]}, {"type": "org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping", "allDeclaredFields": true}, {"type": "org.springframework.boot.autoconfigure.web.servlet.WelcomePageNotAcceptableHandlerMapping", "allDeclaredFields": true}, {"type": "org.springframework.boot.autoconfigure.web.servlet.error.AbstractErrorController", "allDeclaredFields": true}, {"type": "org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController", "allDeclaredFields": true}, {"type": "org.springframework.boot.autoconfigure.web.servlet.error.DefaultErrorViewResolver", "allDeclaredFields": true}, {"type": "org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.boot.autoconfigure.web.ServerProperties"]}, {"name": "basicErrorController", "parameterTypes": ["org.springframework.boot.web.servlet.error.ErrorAttributes", "org.springframework.beans.factory.ObjectProvider"]}, {"name": "errorAttributes", "parameterTypes": []}, {"name": "errorPageCustomizer", "parameterTypes": ["org.springframework.boot.autoconfigure.web.servlet.DispatcherServletPath"]}, {"name": "preserveErrorControllerTargetClassPostProcessor", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$DefaultErrorViewResolverConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.context.ApplicationContext", "org.springframework.boot.autoconfigure.web.WebProperties"]}, {"name": "conventionErrorViewResolver", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$ErrorPageCustomizer", "allDeclaredFields": true}, {"type": "org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$ErrorTemplateMissingCondition", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$PreserveErrorControllerTargetClassPostProcessor"}, {"type": "org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$StaticView", "allDeclaredFields": true}, {"type": "org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$WhitelabelErrorViewConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "beanNameViewResolver", "parameterTypes": []}, {"name": "defaultErrorView", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.web.servlet.error.ErrorViewResolver"}, {"type": "org.springframework.boot.autoconfigure.websocket.servlet.TomcatWebSocketServletWebServerCustomizer", "allDeclaredFields": true}, {"type": "org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration$TomcatWebSocketConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "websocketServletWebServerCustomizer", "parameterTypes": []}]}, {"type": "org.springframework.boot.availability.ApplicationAvailability"}, {"type": "org.springframework.boot.availability.ApplicationAvailabilityBean", "allDeclaredFields": true}, {"type": "org.springframework.boot.builder.ParentContextCloserApplicationListener", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.cloud.CloudFoundryVcapEnvironmentPostProcessor", "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.boot.logging.DeferredLogFactory"]}]}, {"type": "org.springframework.boot.context.ConfigurationWarningsApplicationContextInitializer", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.context.ContextIdApplicationContextInitializer", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.context.FileEncodingApplicationListener", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.context.TypeExcludeFilter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.context.config.AnsiOutputApplicationListener", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.context.config.ConfigDataEnvironmentPostProcessor", "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.boot.logging.DeferredLogFactory", "org.springframework.boot.ConfigurableBootstrapContext"]}]}, {"type": "org.springframework.boot.context.config.ConfigDataNotFoundAction"}, {"type": "org.springframework.boot.context.config.ConfigDataProperties", "fields": [{"name": "this$0"}]}, {"type": "org.springframework.boot.context.config.ConfigTreeConfigDataLoader", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.context.config.ConfigTreeConfigDataLocationResolver", "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.core.io.ResourceLoader"]}]}, {"type": "org.springframework.boot.context.config.StandardConfigDataLoader", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.context.config.StandardConfigDataLocationResolver", "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.boot.logging.DeferredLogFactory", "org.springframework.boot.context.properties.bind.Binder", "org.springframework.core.io.ResourceLoader"]}]}, {"type": "org.springframework.boot.context.event.EventPublishingRunListener", "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.boot.SpringApplication", "java.lang.String[]"]}]}, {"type": "org.springframework.boot.context.logging.LoggingApplicationListener", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.context.properties.BoundConfigurationProperties", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.context.properties.ConfigurationProperties"}, {"type": "org.springframework.boot.context.properties.ConfigurationPropertiesBinder$ConfigurationPropertiesBinderFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.context.properties.ConfigurationPropertiesBinding"}, {"type": "org.springframework.boot.context.properties.ConfigurationPropertiesBindingPostProcessor", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.context.properties.DeprecatedConfigurationProperty"}, {"type": "org.springframework.boot.context.properties.EnableConfigurationProperties"}, {"type": "org.springframework.boot.context.properties.EnableConfigurationPropertiesRegistrar", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.context.properties.NestedConfigurationProperty"}, {"type": "org.springframework.boot.context.properties.bind.Name"}, {"type": "org.springframework.boot.context.properties.bind.Nested"}, {"type": "org.springframework.boot.convert.DurationUnit"}, {"type": "org.springframework.boot.env.EnvironmentPostProcessorApplicationListener", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.env.PropertiesPropertySourceLoader", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.env.RandomValuePropertySourceEnvironmentPostProcessor", "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.boot.logging.DeferredLogFactory"]}]}, {"type": "org.springframework.boot.env.SpringApplicationJsonEnvironmentPostProcessor", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.env.YamlPropertySourceLoader", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.flyway.FlywayDatabaseInitializerDetector", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.http.client.AbstractClientHttpRequestFactoryBuilder", "allDeclaredFields": true}, {"type": "org.springframework.boot.http.client.ClientHttpRequestFactoryBuilder"}, {"type": "org.springframework.boot.http.client.ClientHttpRequestFactorySettings", "allDeclaredFields": true}, {"type": "org.springframework.boot.http.client.JdkClientHttpRequestFactoryBuilder", "allDeclaredFields": true}, {"type": "org.springframework.boot.info.BuildProperties", "allDeclaredFields": true}, {"type": "org.springframework.boot.info.InfoProperties", "allDeclaredFields": true}, {"type": "org.springframework.boot.io.Base64ProtocolResolver", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.io.ProtocolResolverApplicationContextInitializer", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.jackson.JsonComponentModule", "allDeclaredFields": true}, {"type": "org.springframework.boot.jackson.JsonMixinModule", "allDeclaredFields": true}, {"type": "org.springframework.boot.jackson.JsonMixinModuleEntries", "allDeclaredFields": true}, {"type": "org.springframework.boot.jdbc.SchemaManagementProvider"}, {"type": "org.springframework.boot.jdbc.SpringJdbcDependsOnDatabaseInitializationDetector", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.jdbc.init.DataSourceScriptDatabaseInitializer", "allDeclaredFields": true}, {"type": "org.springframework.boot.jdbc.init.DataSourceScriptDatabaseInitializerDetector", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.jdbc.metadata.DataSourcePoolMetadataProvider"}, {"type": "org.springframework.boot.jooq.JooqDependsOnDatabaseInitializationDetector", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.liquibase.LiquibaseDatabaseInitializerDetector", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.loader.launch.JarLauncher"}, {"type": "org.springframework.boot.loader.launch.LaunchedClassLoader"}, {"type": "org.springframework.boot.loader.launch.Launcher"}, {"type": "org.springframework.boot.logging.LogLevelEditor"}, {"type": "org.springframework.boot.logging.java.JavaLoggingSystem$Factory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.logging.java.JavaLoggingSystem.Factory"}, {"type": "org.springframework.boot.logging.log4j2.Log4J2LoggingSystem$Factory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.logging.log4j2.Log4J2LoggingSystem.Factory"}, {"type": "org.springframework.boot.logging.logback.ColorConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.logging.logback.EnclosedInSquareBracketsConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.logging.logback.LogbackLoggingSystem$Factory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.logging.logback.LogbackLoggingSystem.Factory"}, {"type": "org.springframework.boot.logging.logback.RootLogLevelConfigurator"}, {"type": "org.springframework.boot.orm.jpa.JpaDatabaseInitializerDetector", "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.core.env.Environment"]}]}, {"type": "org.springframework.boot.orm.jpa.JpaDependsOnDatabaseInitializationDetector", "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.core.env.Environment"]}]}, {"type": "org.springframework.boot.r2dbc.init.R2dbcScriptDatabaseInitializerDetector", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.reactor.ReactorEnvironmentPostProcessor", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.rsocket.context.RSocketPortInfoApplicationContextInitializer", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.sql.init.AbstractScriptDatabaseInitializer", "allDeclaredFields": true}, {"type": "org.springframework.boot.sql.init.dependency.AnnotationDependsOnDatabaseInitializationDetector", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.sql.init.dependency.DatabaseInitializationDependencyConfigurer", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.sql.init.dependency.DatabaseInitializationDependencyConfigurer$DependsOnDatabaseInitializationPostProcessor", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.ssl.DefaultSslBundleRegistry", "allDeclaredFields": true}, {"type": "org.springframework.boot.ssl.SslBundleRegistry"}, {"type": "org.springframework.boot.ssl.SslBundles"}, {"type": "org.springframework.boot.task.SimpleAsyncTaskExecutorBuilder", "allDeclaredFields": true}, {"type": "org.springframework.boot.task.SimpleAsyncTaskSchedulerBuilder", "allDeclaredFields": true}, {"type": "org.springframework.boot.task.ThreadPoolTaskExecutorBuilder", "allDeclaredFields": true}, {"type": "org.springframework.boot.task.ThreadPoolTaskSchedulerBuilder", "allDeclaredFields": true}, {"type": "org.springframework.boot.type.classreading.ConcurrentReferenceCachingMetadataReaderFactory"}, {"type": "org.springframework.boot.validation.beanvalidation.MethodValidationExcludeFilter", "methods": [{"name": "byAnnotation", "parameterTypes": ["java.lang.Class"]}]}, {"type": "org.springframework.boot.web.client.RestClientCustomizer"}, {"type": "org.springframework.boot.web.client.RestTemplateBuilder"}, {"type": "org.springframework.boot.web.client.RestTemplateCustomizer"}, {"type": "org.springframework.boot.web.context.ServerPortInfoApplicationContextInitializer", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.web.embedded.tomcat.ConfigurableTomcatWebServerFactory"}, {"type": "org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory", "allDeclaredFields": true}, {"type": "org.springframework.boot.web.reactive.context.ReactiveWebServerApplicationContextFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.web.server.AbstractConfigurableWebServerFactory", "allDeclaredFields": true}, {"type": "org.springframework.boot.web.server.ConfigurableWebServerFactory"}, {"type": "org.springframework.boot.web.server.ErrorPageRegistrar"}, {"type": "org.springframework.boot.web.server.ErrorPageRegistrarBeanPostProcessor", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.web.server.ErrorPageRegistry"}, {"type": "org.springframework.boot.web.server.WebServerFactory"}, {"type": "org.springframework.boot.web.server.WebServerFactoryCustomizer"}, {"type": "org.springframework.boot.web.server.WebServerFactoryCustomizerBeanPostProcessor", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.web.servlet.AbstractFilterRegistrationBean", "allDeclaredFields": true}, {"type": "org.springframework.boot.web.servlet.DelegatingFilterProxyRegistrationBean", "allDeclaredFields": true}, {"type": "org.springframework.boot.web.servlet.DelegatingFilterProxyRegistrationBean$1"}, {"type": "org.springframework.boot.web.servlet.DynamicRegistrationBean", "allDeclaredFields": true}, {"type": "org.springframework.boot.web.servlet.FilterRegistrationBean", "allDeclaredFields": true}, {"type": "org.springframework.boot.web.servlet.RegistrationBean", "allDeclaredFields": true}, {"type": "org.springframework.boot.web.servlet.ServletContextInitializer"}, {"type": "org.springframework.boot.web.servlet.ServletRegistrationBean", "allDeclaredFields": true}, {"type": "org.springframework.boot.web.servlet.WebListenerRegistry"}, {"type": "org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext"}, {"type": "org.springframework.boot.web.servlet.context.ServletWebServerApplicationContextFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.web.servlet.error.DefaultErrorAttributes", "allDeclaredFields": true}, {"type": "org.springframework.boot.web.servlet.error.ErrorAttributes"}, {"type": "org.springframework.boot.web.servlet.error.ErrorController"}, {"type": "org.springframework.boot.web.servlet.filter.OrderedCharacterEncodingFilter", "allDeclaredFields": true}, {"type": "org.springframework.boot.web.servlet.filter.OrderedFilter"}, {"type": "org.springframework.boot.web.servlet.filter.OrderedFormContentFilter", "allDeclaredFields": true}, {"type": "org.springframework.boot.web.servlet.filter.OrderedRequestContextFilter", "allDeclaredFields": true}, {"type": "org.springframework.boot.web.servlet.server.AbstractServletWebServerFactory", "allDeclaredFields": true}, {"type": "org.springframework.boot.web.servlet.server.ConfigurableServletWebServerFactory"}, {"type": "org.springframework.boot.web.servlet.server.Encoding", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.boot.web.servlet.server.ServletWebServerFactory"}, {"type": "org.springframework.cache.Cache"}, {"type": "org.springframework.cache.CacheManager"}, {"type": "org.springframework.cache.interceptor.CacheAspectSupport"}, {"type": "org.springframework.context.ApplicationContextAware"}, {"type": "org.springframework.context.ApplicationEventPublisherAware"}, {"type": "org.springframework.context.ApplicationListener"}, {"type": "org.springframework.context.ApplicationStartupAware"}, {"type": "org.springframework.context.EmbeddedValueResolverAware"}, {"type": "org.springframework.context.EnvironmentAware"}, {"type": "org.springframework.context.Lifecycle"}, {"type": "org.springframework.context.LifecycleProcessor"}, {"type": "org.springframework.context.MessageSourceAware"}, {"type": "org.springframework.context.Phased"}, {"type": "org.springframework.context.ResourceLoaderAware"}, {"type": "org.springframework.context.SmartLifecycle"}, {"type": "org.springframework.context.annotation.AnnotationScopeMetadataResolver", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.context.annotation.AutoProxyRegistrar", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.context.annotation.Bean"}, {"type": "org.springframework.context.annotation.CommonAnnotationBeanPostProcessor", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.context.annotation.ComponentScan"}, {"type": "org.springframework.context.annotation.ComponentScan$Filter"}, {"type": "org.springframework.context.annotation.Conditional"}, {"type": "org.springframework.context.annotation.Configuration"}, {"type": "org.springframework.context.annotation.ConfigurationClassEnhancer$EnhancedConfiguration"}, {"type": "org.springframework.context.annotation.ConfigurationClassParser$DefaultDeferredImportSelectorGroup", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.context.annotation.ConfigurationClassPostProcessor", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "setMetadataReaderFactory", "parameterTypes": ["org.springframework.core.type.classreading.MetadataReaderFactory"]}]}, {"type": "org.springframework.context.annotation.DependsOn"}, {"type": "org.springframework.context.annotation.Import"}, {"type": "org.springframework.context.annotation.ImportAware"}, {"type": "org.springframework.context.annotation.ImportRuntimeHints"}, {"type": "org.springframework.context.annotation.Lazy"}, {"type": "org.springframework.context.annotation.Primary"}, {"type": "org.springframework.context.annotation.Role"}, {"type": "org.springframework.context.annotation.Scope"}, {"type": "org.springframework.context.event.DefaultEventListenerFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.context.event.EventListenerMethodProcessor", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.context.event.GenericApplicationListener"}, {"type": "org.springframework.context.event.SmartApplicationListener"}, {"type": "org.springframework.context.support.AbstractApplicationContext"}, {"type": "org.springframework.context.support.ApplicationObjectSupport", "allDeclaredFields": true}, {"type": "org.springframework.context.support.DefaultLifecycleProcessor", "allDeclaredFields": true}, {"type": "org.springframework.context.support.PropertySourcesPlaceholderConfigurer"}, {"type": "org.springframework.core.Ordered"}, {"type": "org.springframework.core.PriorityOrdered"}, {"type": "org.springframework.core.annotation.AliasFor"}, {"type": "org.springframework.core.annotation.Order"}, {"type": "org.springframework.core.convert.ConversionService"}, {"type": "org.springframework.core.convert.converter.ConverterRegistry"}, {"type": "org.springframework.core.convert.converter.GenericConverter"}, {"type": "org.springframework.core.convert.support.ConfigurableConversionService"}, {"type": "org.springframework.core.convert.support.GenericConversionService", "allDeclaredFields": true}, {"type": "org.springframework.core.env.EnvironmentCapable"}, {"type": "org.springframework.core.type.classreading.MetadataReaderFactory"}, {"type": "org.springframework.dao.annotation.PersistenceExceptionTranslationPostProcessor", "allDeclaredFields": true}, {"type": "org.springframework.data.cassandra.ReactiveSession"}, {"type": "org.springframework.data.elasticsearch.client.elc.ElasticsearchTemplate"}, {"type": "org.springframework.data.elasticsearch.repository.ElasticsearchRepository"}, {"type": "org.springframework.data.jdbc.repository.config.AbstractJdbcConfiguration"}, {"type": "org.springframework.data.jpa.repository.JpaRepository"}, {"type": "org.springframework.data.ldap.repository.LdapRepository"}, {"type": "org.springframework.data.r2dbc.core.R2dbcEntityTemplate"}, {"type": "org.springframework.data.redis.connection.RedisConnectionFactory"}, {"type": "org.springframework.data.redis.core.RedisOperations"}, {"type": "org.springframework.data.redis.repository.configuration.EnableRedisRepositories"}, {"type": "org.springframework.data.rest.webmvc.alps.AlpsJsonHttpMessageConverter"}, {"type": "org.springframework.data.rest.webmvc.config.RepositoryRestMvcConfiguration"}, {"type": "org.springframework.data.web.PageableHandlerMethodArgumentResolver"}, {"type": "org.springframework.format.FormatterRegistry"}, {"type": "org.springframework.format.support.DefaultFormattingConversionService", "allDeclaredFields": true}, {"type": "org.springframework.format.support.FormattingConversionService", "allDeclaredFields": true}, {"type": "org.springframework.graphql.execution.DataFetcherExceptionResolverAdapter"}, {"type": "org.springframework.hateoas.EntityModel"}, {"type": "org.springframework.hateoas.server.mvc.TypeConstrainedMappingJackson2HttpMessageConverter"}, {"type": "org.springframework.http.ReactiveHttpInputMessage"}, {"type": "org.springframework.http.client.ClientHttpRequestFactory"}, {"type": "org.springframework.http.codec.CodecConfigurer"}, {"type": "org.springframework.http.codec.multipart.DefaultPartHttpMessageReader"}, {"type": "org.springframework.http.converter.AbstractGenericHttpMessageConverter", "allDeclaredFields": true}, {"type": "org.springframework.http.converter.AbstractHttpMessageConverter", "allDeclaredFields": true}, {"type": "org.springframework.http.converter.GenericHttpMessageConverter"}, {"type": "org.springframework.http.converter.HttpMessageConverter"}, {"type": "org.springframework.http.converter.StringHttpMessageConverter", "allDeclaredFields": true}, {"type": "org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter", "allDeclaredFields": true}, {"type": "org.springframework.http.converter.json.GsonHttpMessageConverter"}, {"type": "org.springframework.http.converter.json.Jackson2ObjectMapperBuilder", "allDeclaredFields": true}, {"type": "org.springframework.http.converter.json.MappingJackson2HttpMessageConverter", "allDeclaredFields": true}, {"type": "org.springframework.http.server.reactive.HttpHandler"}, {"type": "org.springframework.integration.config.EnableIntegration"}, {"type": "org.springframework.jdbc.core.JdbcOperations"}, {"type": "org.springframework.jdbc.core.JdbcTemplate", "allDeclaredFields": true}, {"type": "org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations"}, {"type": "org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate", "allDeclaredFields": true}, {"type": "org.springframework.jdbc.core.simple.DefaultJdbcClient", "allDeclaredFields": true}, {"type": "org.springframework.jdbc.core.simple.JdbcClient"}, {"type": "org.springframework.jdbc.datasource.DataSourceTransactionManager", "allDeclaredFields": true}, {"type": "org.springframework.jdbc.datasource.embedded.EmbeddedDatabaseType"}, {"type": "org.springframework.jdbc.datasource.init.DatabasePopulator"}, {"type": "org.springframework.jdbc.support.JdbcAccessor", "allDeclaredFields": true}, {"type": "org.springframework.jdbc.support.JdbcTransactionManager", "allDeclaredFields": true}, {"type": "org.springframework.jdbc.support.JdbcUtils"}, {"type": "org.springframework.jms.core.JmsTemplate"}, {"type": "org.springframework.jmx.export.MBeanExporter"}, {"type": "org.springframework.jmx.export.annotation.ManagedAttribute"}, {"type": "org.springframework.jmx.export.annotation.ManagedOperation"}, {"type": "org.springframework.jmx.export.annotation.ManagedResource"}, {"type": "org.springframework.kafka.core.KafkaTemplate"}, {"type": "org.springframework.ldap.core.ContextSource"}, {"type": "org.springframework.mail.javamail.JavaMailSenderImpl"}, {"type": "org.springframework.messaging.core.AbstractMessageReceivingTemplate", "allDeclaredFields": true}, {"type": "org.springframework.messaging.core.AbstractMessageSendingTemplate", "allDeclaredFields": true}, {"type": "org.springframework.messaging.core.AbstractMessagingTemplate", "allDeclaredFields": true}, {"type": "org.springframework.messaging.core.MessageReceivingOperations"}, {"type": "org.springframework.messaging.core.MessageRequestReplyOperations"}, {"type": "org.springframework.messaging.core.MessageSendingOperations"}, {"type": "org.springframework.messaging.rsocket.annotation.support.RSocketMessageHandler"}, {"type": "org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean"}, {"type": "org.springframework.oxm.Marshaller"}, {"type": "org.springframework.r2dbc.connection.R2dbcTransactionManager"}, {"type": "org.springframework.r2dbc.connection.init.DatabasePopulator"}, {"type": "org.springframework.rabbit.stream.config.StreamRabbitListenerContainerFactory"}, {"type": "org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor"}, {"type": "org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler"}, {"type": "org.springframework.scheduling.quartz.SchedulerFactoryBean"}, {"type": "org.springframework.security.access.expression.AbstractSecurityExpressionHandler", "allDeclaredFields": true}, {"type": "org.springframework.security.access.expression.SecurityExpressionHandler"}, {"type": "org.springframework.security.authentication.AnonymousAuthenticationProvider", "allDeclaredFields": true}, {"type": "org.springframework.security.authentication.AuthenticationEventPublisher"}, {"type": "org.springframework.security.authentication.AuthenticationManager"}, {"type": "org.springframework.security.authentication.AuthenticationProvider"}, {"type": "org.springframework.security.authentication.DefaultAuthenticationEventPublisher", "allDeclaredFields": true}, {"type": "org.springframework.security.authentication.ProviderManager", "allDeclaredFields": true}, {"type": "org.springframework.security.authentication.ReactiveAuthenticationManager"}, {"type": "org.springframework.security.authentication.event.AuthenticationFailureBadCredentialsEvent"}, {"type": "org.springframework.security.authentication.event.AuthenticationFailureCredentialsExpiredEvent"}, {"type": "org.springframework.security.authentication.event.AuthenticationFailureDisabledEvent"}, {"type": "org.springframework.security.authentication.event.AuthenticationFailureExpiredEvent"}, {"type": "org.springframework.security.authentication.event.AuthenticationFailureLockedEvent"}, {"type": "org.springframework.security.authentication.event.AuthenticationFailureProviderNotFoundEvent"}, {"type": "org.springframework.security.authentication.event.AuthenticationFailureProxyUntrustedEvent"}, {"type": "org.springframework.security.authentication.event.AuthenticationFailureServiceExceptionEvent"}, {"type": "org.springframework.security.authorization.AuthorizationManager"}, {"type": "org.springframework.security.config.ObjectPostProcessor"}, {"type": "org.springframework.security.config.annotation.AbstractConfiguredSecurityBuilder", "allDeclaredFields": true}, {"type": "org.springframework.security.config.annotation.AbstractSecurityBuilder", "allDeclaredFields": true}, {"type": "org.springframework.security.config.annotation.SecurityBuilder"}, {"type": "org.springframework.security.config.annotation.SecurityConfigurer"}, {"type": "org.springframework.security.config.annotation.authentication.ProviderManagerBuilder"}, {"type": "org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder", "allDeclaredFields": true}, {"type": "org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "authenticationManagerBuilder", "parameterTypes": ["org.springframework.security.config.ObjectPostProcessor", "org.springframework.context.ApplicationContext"]}, {"name": "enableGlobalAuthenticationAutowiredConfigurer", "parameterTypes": ["org.springframework.context.ApplicationContext"]}, {"name": "initializeAuthenticationProviderBeanManagerConfigurer", "parameterTypes": ["org.springframework.context.ApplicationContext"]}, {"name": "initializeUserDetailsBeanManagerConfigurer", "parameterTypes": ["org.springframework.context.ApplicationContext"]}, {"name": "setApplicationContext", "parameterTypes": ["org.springframework.context.ApplicationContext"]}, {"name": "setGlobalAuthenticationConfigurers", "parameterTypes": ["java.util.List"]}, {"name": "setObjectPostProcessor", "parameterTypes": ["org.springframework.security.config.ObjectPostProcessor"]}]}, {"type": "org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration$DefaultPasswordEncoderAuthenticationManagerBuilder", "allDeclaredFields": true}, {"type": "org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration$EnableGlobalAuthenticationAutowiredConfigurer", "allDeclaredFields": true}, {"type": "org.springframework.security.config.annotation.authentication.configuration.EnableGlobalAuthentication"}, {"type": "org.springframework.security.config.annotation.authentication.configuration.GlobalAuthenticationConfigurerAdapter", "allDeclaredFields": true}, {"type": "org.springframework.security.config.annotation.authentication.configuration.InitializeAuthenticationProviderBeanManagerConfigurer", "allDeclaredFields": true}, {"type": "org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer", "allDeclaredFields": true}, {"type": "org.springframework.security.config.annotation.configuration.AutowireBeanFactoryObjectPostProcessor", "allDeclaredFields": true}, {"type": "org.springframework.security.config.annotation.configuration.ObjectPostProcessorConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "objectPostProcessor", "parameterTypes": ["org.springframework.beans.factory.config.AutowireCapableBeanFactory"]}]}, {"type": "org.springframework.security.config.annotation.web.HttpSecurityBuilder"}, {"type": "org.springframework.security.config.annotation.web.builders.HttpSecurity", "allDeclaredFields": true}, {"type": "org.springframework.security.config.annotation.web.builders.WebSecurity", "allDeclaredFields": true}, {"type": "org.springframework.security.config.annotation.web.configuration.EnableWebSecurity"}, {"type": "org.springframework.security.config.annotation.web.configuration.HttpSecurityConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "httpSecurity", "parameterTypes": []}, {"name": "setApplicationContext", "parameterTypes": ["org.springframework.context.ApplicationContext"]}, {"name": "setAuthenticationConfiguration", "parameterTypes": ["org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration"]}, {"name": "setContentNegotiationStrategy", "parameterTypes": ["org.springframework.web.accept.ContentNegotiationStrategy"]}, {"name": "setObjectPostProcessor", "parameterTypes": ["org.springframework.security.config.ObjectPostProcessor"]}]}, {"type": "org.springframework.security.config.annotation.web.configuration.OAuth2ImportSelector", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.security.config.annotation.web.configuration.ObservationConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "authenticationManagerPostProcessor", "parameterTypes": ["org.springframework.beans.factory.ObjectProvider", "org.springframework.beans.factory.ObjectProvider"]}, {"name": "filterChainDecoratorPostProcessor", "parameterTypes": ["org.springframework.beans.factory.ObjectProvider", "org.springframework.beans.factory.ObjectProvider"]}, {"name": "webAuthorizationManagerPostProcessor", "parameterTypes": ["org.springframework.beans.factory.ObjectProvider", "org.springframework.beans.factory.ObjectProvider"]}]}, {"type": "org.springframework.security.config.annotation.web.configuration.ObservationConfiguration$1", "allDeclaredFields": true}, {"type": "org.springframework.security.config.annotation.web.configuration.ObservationConfiguration$2", "allDeclaredFields": true}, {"type": "org.springframework.security.config.annotation.web.configuration.ObservationConfiguration$3", "allDeclaredFields": true}, {"type": "org.springframework.security.config.annotation.web.configuration.ObservationImportSelector", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.security.config.annotation.web.configuration.SpringWebMvcImportSelector", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "requestDataValueProcessor", "parameterTypes": []}, {"name": "springSecurityHandlerMappingIntrospectorBeanDefinitionRegistryPostProcessor", "parameterTypes": []}]}, {"type": "org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$1"}, {"type": "org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": ["java.util.List"]}]}, {"type": "org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$HandlerMappingIntrospectorCacheFilterFactoryBean", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.security.config.annotation.web.configuration.WebSecurityConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "conversionServicePostProcessor", "parameterTypes": []}, {"name": "delegatingApplicationListener", "parameterTypes": []}, {"name": "privilegeEvaluator", "parameterTypes": []}, {"name": "setFilterChainProxySecurityConfigurer", "parameterTypes": ["org.springframework.security.config.ObjectPostProcessor", "org.springframework.beans.factory.config.ConfigurableListableBeanFactory"]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parameterTypes": ["java.util.List"]}, {"name": "springSecurityFilterChain", "parameterTypes": []}, {"name": "webSecurityExpressionHandler", "parameterTypes": []}]}, {"type": "org.springframework.security.config.annotation.web.reactive.EnableWebFluxSecurity"}, {"type": "org.springframework.security.config.crypto.RsaKeyConversionServicePostProcessor"}, {"type": "org.springframework.security.config.http.SessionCreationPolicy"}, {"type": "org.springframework.security.context.DelegatingApplicationListener", "allDeclaredFields": true}, {"type": "org.springframework.security.core.annotation.AuthenticationPrincipal"}, {"type": "org.springframework.security.core.context.SecurityContextHolder"}, {"type": "org.springframework.security.data.repository.query.SecurityEvaluationContextExtension"}, {"type": "org.springframework.security.oauth2.client.registration.ClientRegistration"}, {"type": "org.springframework.security.oauth2.client.registration.ClientRegistrationRepository"}, {"type": "org.springframework.security.oauth2.server.authorization.OAuth2Authorization"}, {"type": "org.springframework.security.oauth2.server.resource.BearerTokenError"}, {"type": "org.springframework.security.oauth2.server.resource.authentication.BearerTokenAuthenticationToken"}, {"type": "org.springframework.security.oauth2.server.resource.introspection.ReactiveOpaqueTokenIntrospector"}, {"type": "org.springframework.security.rsocket.core.SecuritySocketAcceptorInterceptor"}, {"type": "org.springframework.security.saml2.provider.service.registration.RelyingPartyRegistrationRepository"}, {"type": "org.springframework.security.web.DefaultSecurityFilterChain", "allDeclaredFields": true}, {"type": "org.springframework.security.web.FilterChainProxy", "allDeclaredFields": true}, {"type": "org.springframework.security.web.FilterChainProxy$VirtualFilterChain"}, {"type": "org.springframework.security.web.SecurityFilterChain"}, {"type": "org.springframework.security.web.access.AuthorizationManagerWebInvocationPrivilegeEvaluator$HttpServletRequestTransformer"}, {"type": "org.springframework.security.web.access.ExceptionTranslationFilter", "allDeclaredFields": true}, {"type": "org.springframework.security.web.access.HandlerMappingIntrospectorRequestTransformer", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.web.servlet.handler.HandlerMappingIntrospector"]}]}, {"type": "org.springframework.security.web.access.RequestMatcherDelegatingWebInvocationPrivilegeEvaluator", "allDeclaredFields": true}, {"type": "org.springframework.security.web.access.WebInvocationPrivilegeEvaluator"}, {"type": "org.springframework.security.web.access.expression.DefaultWebSecurityExpressionHandler", "allDeclaredFields": true}, {"type": "org.springframework.security.web.access.intercept.AuthorizationFilter", "allDeclaredFields": true}, {"type": "org.springframework.security.web.access.intercept.RequestMatcherDelegatingAuthorizationManager", "allDeclaredFields": true}, {"type": "org.springframework.security.web.authentication.AnonymousAuthenticationFilter"}, {"type": "org.springframework.security.web.authentication.logout.LogoutFilter", "allDeclaredFields": true}, {"type": "org.springframework.security.web.authentication.logout.LogoutHandler"}, {"type": "org.springframework.security.web.authentication.logout.LogoutSuccessEventPublishingLogoutHandler", "allDeclaredFields": true}, {"type": "org.springframework.security.web.authentication.session.AbstractSessionFixationProtectionStrategy", "allDeclaredFields": true}, {"type": "org.springframework.security.web.authentication.session.ChangeSessionIdAuthenticationStrategy", "allDeclaredFields": true}, {"type": "org.springframework.security.web.authentication.session.CompositeSessionAuthenticationStrategy", "allDeclaredFields": true}, {"type": "org.springframework.security.web.authentication.session.SessionAuthenticationStrategy"}, {"type": "org.springframework.security.web.context.AbstractSecurityWebApplicationInitializer"}, {"type": "org.springframework.security.web.context.SecurityContextHolderFilter", "allDeclaredFields": true}, {"type": "org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter"}, {"type": "org.springframework.security.web.header.HeaderWriterFilter", "allDeclaredFields": true}, {"type": "org.springframework.security.web.savedrequest.RequestCacheAwareFilter", "allDeclaredFields": true}, {"type": "org.springframework.security.web.servlet.support.csrf.CsrfRequestDataValueProcessor", "allDeclaredFields": true}, {"type": "org.springframework.security.web.servlet.util.matcher.MvcRequestMatcher", "allDeclaredFields": true}, {"type": "org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter", "allDeclaredFields": true}, {"type": "org.springframework.security.web.session.DisableEncodeUrlFilter"}, {"type": "org.springframework.security.web.session.SessionManagementFilter", "allDeclaredFields": true}, {"type": "org.springframework.security.web.util.matcher.RequestMatcher"}, {"type": "org.springframework.security.web.util.matcher.RequestVariablesExtractor"}, {"type": "org.springframework.session.Session"}, {"type": "org.springframework.stereotype.Component"}, {"type": "org.springframework.stereotype.Controller"}, {"type": "org.springframework.stereotype.Indexed"}, {"type": "org.springframework.transaction.ConfigurableTransactionManager"}, {"type": "org.springframework.transaction.PlatformTransactionManager"}, {"type": "org.springframework.transaction.ReactiveTransactionManager"}, {"type": "org.springframework.transaction.TransactionDefinition"}, {"type": "org.springframework.transaction.TransactionManager"}, {"type": "org.springframework.transaction.annotation.AbstractTransactionManagementConfiguration", "allDeclaredFields": true, "methods": [{"name": "transactionAttributeSource", "parameterTypes": []}, {"name": "transactionalEventListenerFactory", "parameterTypes": []}]}, {"type": "org.springframework.transaction.annotation.AnnotationTransactionAttributeSource", "allDeclaredFields": true}, {"type": "org.springframework.transaction.annotation.EnableTransactionManagement"}, {"type": "org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "transactionAdvisor", "parameterTypes": ["org.springframework.transaction.interceptor.TransactionAttributeSource", "org.springframework.transaction.interceptor.TransactionInterceptor"]}, {"name": "transactionInterceptor", "parameterTypes": ["org.springframework.transaction.interceptor.TransactionAttributeSource"]}]}, {"type": "org.springframework.transaction.annotation.RestrictedTransactionalEventListenerFactory"}, {"type": "org.springframework.transaction.annotation.TransactionManagementConfigurationSelector", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.springframework.transaction.annotation.TransactionRuntimeHints"}, {"type": "org.springframework.transaction.aspectj.AbstractTransactionAspect"}, {"type": "org.springframework.transaction.interceptor.AbstractFallbackTransactionAttributeSource", "allDeclaredFields": true}, {"type": "org.springframework.transaction.interceptor.BeanFactoryTransactionAttributeSourceAdvisor", "allDeclaredFields": true}, {"type": "org.springframework.transaction.interceptor.TransactionAspectSupport", "allDeclaredFields": true}, {"type": "org.springframework.transaction.interceptor.TransactionAttributeSource"}, {"type": "org.springframework.transaction.interceptor.TransactionInterceptor", "allDeclaredFields": true}, {"type": "org.springframework.transaction.support.AbstractPlatformTransactionManager", "allDeclaredFields": true}, {"type": "org.springframework.transaction.support.DefaultTransactionDefinition", "allDeclaredFields": true}, {"type": "org.springframework.transaction.support.ResourceTransactionManager"}, {"type": "org.springframework.transaction.support.TransactionOperations"}, {"type": "org.springframework.transaction.support.TransactionTemplate", "allDeclaredFields": true}, {"type": "org.springframework.util.AntPathMatcher", "allDeclaredFields": true}, {"type": "org.springframework.util.PathMatcher"}, {"type": "org.springframework.validation.Validator"}, {"type": "org.springframework.web.accept.ContentNegotiationManager", "allDeclaredFields": true}, {"type": "org.springframework.web.accept.ContentNegotiationStrategy"}, {"type": "org.springframework.web.accept.MediaTypeFileExtensionResolver"}, {"type": "org.springframework.web.bind.annotation.ExceptionHandler"}, {"type": "org.springframework.web.bind.annotation.GetMapping"}, {"type": "org.springframework.web.bind.annotation.Mapping"}, {"type": "org.springframework.web.bind.annotation.PostMapping"}, {"type": "org.springframework.web.bind.annotation.RequestBody"}, {"type": "org.springframework.web.bind.annotation.RequestMapping"}, {"type": "org.springframework.web.bind.annotation.RequestParam"}, {"type": "org.springframework.web.bind.annotation.ResponseBody"}, {"type": "org.springframework.web.bind.annotation.RestController"}, {"type": "org.springframework.web.client.RestClient"}, {"type": "org.springframework.web.client.RestClient$Builder"}, {"type": "org.springframework.web.client.RestTemplate"}, {"type": "org.springframework.web.context.ConfigurableWebApplicationContext"}, {"type": "org.springframework.web.context.ServletContextAware"}, {"type": "org.springframework.web.context.request.RequestContextListener"}, {"type": "org.springframework.web.context.support.GenericWebApplicationContext"}, {"type": "org.springframework.web.context.support.ServletContextResource"}, {"type": "org.springframework.web.context.support.WebApplicationObjectSupport", "allDeclaredFields": true}, {"type": "org.springframework.web.cors.CorsConfigurationSource"}, {"type": "org.springframework.web.cors.PreFlightRequestHandler"}, {"type": "org.springframework.web.cors.UrlBasedCorsConfigurationSource", "allDeclaredFields": true}, {"type": "org.springframework.web.filter.CharacterEncodingFilter", "allDeclaredFields": true}, {"type": "org.springframework.web.filter.CompositeFilter"}, {"type": "org.springframework.web.filter.CompositeFilter$VirtualFilterChain"}, {"type": "org.springframework.web.filter.CorsFilter"}, {"type": "org.springframework.web.filter.DelegatingFilterProxy"}, {"type": "org.springframework.web.filter.FormContentFilter", "allDeclaredFields": true}, {"type": "org.springframework.web.filter.GenericFilterBean", "allDeclaredFields": true}, {"type": "org.springframework.web.filter.OncePerRequestFilter", "allDeclaredFields": true}, {"type": "org.springframework.web.filter.RequestContextFilter", "allDeclaredFields": true}, {"type": "org.springframework.web.method.annotation.ExceptionHandlerMethodResolver"}, {"type": "org.springframework.web.method.support.CompositeUriComponentsContributor", "allDeclaredFields": true}, {"type": "org.springframework.web.method.support.InvocableHandlerMethod"}, {"type": "org.springframework.web.method.support.UriComponentsContributor"}, {"type": "org.springframework.web.multipart.MultipartResolver"}, {"type": "org.springframework.web.multipart.support.StandardServletMultipartResolver", "allDeclaredFields": true}, {"type": "org.springframework.web.reactive.DispatcherHandler"}, {"type": "org.springframework.web.reactive.HandlerResult"}, {"type": "org.springframework.web.reactive.config.WebFluxConfigurer"}, {"type": "org.springframework.web.reactive.function.client.ExchangeFilterFunction"}, {"type": "org.springframework.web.reactive.function.client.WebClient"}, {"type": "org.springframework.web.servlet.DispatcherServlet", "allDeclaredFields": true}, {"type": "org.springframework.web.servlet.FlashMapManager"}, {"type": "org.springframework.web.servlet.FrameworkServlet", "allDeclaredFields": true}, {"type": "org.springframework.web.servlet.HandlerAdapter"}, {"type": "org.springframework.web.servlet.HandlerExceptionResolver"}, {"type": "org.springframework.web.servlet.HandlerMapping"}, {"type": "org.springframework.web.servlet.HttpServletBean", "allDeclaredFields": true}, {"type": "org.springframework.web.servlet.LocaleResolver"}, {"type": "org.springframework.web.servlet.RequestToViewNameTranslator"}, {"type": "org.springframework.web.servlet.ThemeResolver"}, {"type": "org.springframework.web.servlet.View"}, {"type": "org.springframework.web.servlet.ViewResolver"}, {"type": "org.springframework.web.servlet.config.annotation.DelegatingWebMvcConfiguration", "allDeclaredFields": true, "methods": [{"name": "setConfigurers", "parameterTypes": ["java.util.List"]}]}, {"type": "org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport", "allDeclaredFields": true, "methods": [{"name": "beanNameHandlerMapping", "parameterTypes": ["org.springframework.format.support.FormattingConversionService", "org.springframework.web.servlet.resource.ResourceUrlProvider"]}, {"name": "defaultServletHandlerMapping", "parameterTypes": []}, {"name": "handlerExceptionResolver", "parameterTypes": ["org.springframework.web.accept.ContentNegotiationManager"]}, {"name": "handlerFunctionAdapter", "parameterTypes": []}, {"name": "httpRequestHandlerAdapter", "parameterTypes": []}, {"name": "mvcHandlerMappingIntrospector", "parameterTypes": []}, {"name": "mvcPathMatcher", "parameterTypes": []}, {"name": "mvcPatternParser", "parameterTypes": []}, {"name": "mvcResourceUrlProvider", "parameterTypes": []}, {"name": "mvcUriComponentsContributor", "parameterTypes": ["org.springframework.format.support.FormattingConversionService", "org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter"]}, {"name": "mvcUrlPathHelper", "parameterTypes": []}, {"name": "mvcViewResolver", "parameterTypes": ["org.springframework.web.accept.ContentNegotiationManager"]}, {"name": "requestMappingHandlerAdapter", "parameterTypes": ["org.springframework.web.accept.ContentNegotiationManager", "org.springframework.format.support.FormattingConversionService", "org.springframework.validation.Validator"]}, {"name": "requestMappingHandlerMapping", "parameterTypes": ["org.springframework.web.accept.ContentNegotiationManager", "org.springframework.format.support.FormattingConversionService", "org.springframework.web.servlet.resource.ResourceUrlProvider"]}, {"name": "resourceHandlerMapping", "parameterTypes": ["org.springframework.web.accept.ContentNegotiationManager", "org.springframework.format.support.FormattingConversionService", "org.springframework.web.servlet.resource.ResourceUrlProvider"]}, {"name": "routerFunctionMapping", "parameterTypes": ["org.springframework.format.support.FormattingConversionService", "org.springframework.web.servlet.resource.ResourceUrlProvider"]}, {"name": "simpleControllerHandlerAdapter", "parameterTypes": []}, {"name": "viewControllerHandlerMapping", "parameterTypes": ["org.springframework.format.support.FormattingConversionService", "org.springframework.web.servlet.resource.ResourceUrlProvider"]}]}, {"type": "org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport$NoOpValidator", "allDeclaredFields": true}, {"type": "org.springframework.web.servlet.config.annotation.WebMvcConfigurer"}, {"type": "org.springframework.web.servlet.function.support.HandlerFunctionAdapter", "allDeclaredFields": true}, {"type": "org.springframework.web.servlet.function.support.RouterFunctionMapping", "allDeclaredFields": true}, {"type": "org.springframework.web.servlet.handler.AbstractDetectingUrlHandlerMapping", "allDeclaredFields": true}, {"type": "org.springframework.web.servlet.handler.AbstractHandlerMapping", "allDeclaredFields": true}, {"type": "org.springframework.web.servlet.handler.AbstractHandlerMethodMapping", "allDeclaredFields": true}, {"type": "org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$EmptyHandler"}, {"type": "org.springframework.web.servlet.handler.AbstractUrlHandlerMapping", "allDeclaredFields": true}, {"type": "org.springframework.web.servlet.handler.BeanNameUrlHandlerMapping", "allDeclaredFields": true}, {"type": "org.springframework.web.servlet.handler.HandlerExceptionResolverComposite", "allDeclaredFields": true}, {"type": "org.springframework.web.servlet.handler.HandlerMappingIntrospector", "allDeclaredFields": true}, {"type": "org.springframework.web.servlet.handler.MatchableHandlerMapping"}, {"type": "org.springframework.web.servlet.handler.SimpleUrlHandlerMapping", "allDeclaredFields": true}, {"type": "org.springframework.web.servlet.i18n.AbstractLocaleResolver", "allDeclaredFields": true}, {"type": "org.springframework.web.servlet.i18n.AcceptHeaderLocaleResolver", "allDeclaredFields": true}, {"type": "org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter", "allDeclaredFields": true}, {"type": "org.springframework.web.servlet.mvc.SimpleControllerHandlerAdapter", "allDeclaredFields": true}, {"type": "org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter", "allDeclaredFields": true}, {"type": "org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping", "allDeclaredFields": true}, {"type": "org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping$HttpOptionsHandler"}, {"type": "org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter", "allDeclaredFields": true}, {"type": "org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping", "allDeclaredFields": true}, {"type": "org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod"}, {"type": "org.springframework.web.servlet.resource.ResourceUrlProvider", "allDeclaredFields": true}, {"type": "org.springframework.web.servlet.support.AbstractFlashMapManager", "allDeclaredFields": true}, {"type": "org.springframework.web.servlet.support.RequestDataValueProcessor"}, {"type": "org.springframework.web.servlet.support.SessionFlashMapManager", "allDeclaredFields": true}, {"type": "org.springframework.web.servlet.support.WebContentGenerator", "allDeclaredFields": true}, {"type": "org.springframework.web.servlet.theme.AbstractThemeResolver", "allDeclaredFields": true}, {"type": "org.springframework.web.servlet.theme.FixedThemeResolver", "allDeclaredFields": true}, {"type": "org.springframework.web.servlet.view.AbstractCachingViewResolver", "allDeclaredFields": true}, {"type": "org.springframework.web.servlet.view.BeanNameViewResolver", "allDeclaredFields": true}, {"type": "org.springframework.web.servlet.view.ContentNegotiatingViewResolver", "allDeclaredFields": true}, {"type": "org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator", "allDeclaredFields": true}, {"type": "org.springframework.web.servlet.view.InternalResourceViewResolver", "allDeclaredFields": true}, {"type": "org.springframework.web.servlet.view.UrlBasedViewResolver", "allDeclaredFields": true}, {"type": "org.springframework.web.servlet.view.ViewResolverComposite", "allDeclaredFields": true}, {"type": "org.springframework.web.socket.config.annotation.WebSocketMessageBrokerConfigurer"}, {"type": "org.springframework.web.util.UrlPathHelper", "allDeclaredFields": true}, {"type": "org.springframework.web.util.pattern.PathPatternParser", "allDeclaredFields": true}, {"type": "org.springframework.ws.transport.http.MessageDispatcherServlet"}, {"type": "org.thymeleaf.spring6.SpringTemplateEngine"}, {"type": "org.webjars.WebJarAssetLocator"}, {"type": "org.webjars.WebJarVersionLocator"}, {"type": "org.xnio.SslClientAuthMode"}, {"type": "reactor.core.publisher.Flux"}, {"type": "reactor.core.publisher.Hooks"}, {"type": "reactor.core.publisher.Mono"}, {"type": "reactor.core.scheduler.Schedulers"}, {"type": "reactor.netty.http.client.HttpClient"}, {"type": "reactor.netty.http.server.HttpServer"}, {"type": "reactor.tools.agent.ReactorDebugAgent"}, {"type": "sun.security.pkcs12.PKCS12KeyStore", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "sun.security.pkcs12.PKCS12KeyStore$DualFormatPKCS12", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "sun.security.provider.DSA$SHA224withDSA", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "sun.security.provider.DSA$SHA256withDSA", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "sun.security.provider.JavaKeyStore$DualFormatJKS", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "sun.security.provider.JavaKeyStore$JKS", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "sun.security.provider.NativePRNG", "methods": [{"name": "<init>", "parameterTypes": ["java.security.SecureRandomParameters"]}]}, {"type": "sun.security.provider.SHA", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "sun.security.provider.SHA2$SHA224", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "sun.security.provider.SHA2$SHA256", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "sun.security.provider.SHA5$SHA384", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "sun.security.provider.SHA5$SHA512", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "sun.security.provider.X509Factory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "sun.security.provider.certpath.PKIXCertPathValidator", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "sun.security.rsa.PSSParameters", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "sun.security.rsa.RSAKeyFactory$Legacy", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "sun.security.rsa.RSAPSSSignature", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "sun.security.rsa.RSASignature$SHA224withRSA", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "sun.security.rsa.RSASignature$SHA256withRSA", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "sun.security.ssl.KeyManagerFactoryImpl$SunX509", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "sun.security.ssl.SSLContextImpl$DefaultSSLContext", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "sun.security.ssl.TrustManagerFactoryImpl$PKIXFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "sun.security.x509.AuthorityInfoAccessExtension", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Bo<PERSON>an", "java.lang.Object"]}]}, {"type": "sun.security.x509.AuthorityKeyIdentifierExtension", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Bo<PERSON>an", "java.lang.Object"]}]}, {"type": "sun.security.x509.BasicConstraintsExtension", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Bo<PERSON>an", "java.lang.Object"]}]}, {"type": "sun.security.x509.CRLDistributionPointsExtension", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Bo<PERSON>an", "java.lang.Object"]}]}, {"type": "sun.security.x509.CertificatePoliciesExtension", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Bo<PERSON>an", "java.lang.Object"]}]}, {"type": "sun.security.x509.ExtendedKeyUsageExtension", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Bo<PERSON>an", "java.lang.Object"]}]}, {"type": "sun.security.x509.IssuerAlternativeNameExtension", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Bo<PERSON>an", "java.lang.Object"]}]}, {"type": "sun.security.x509.KeyUsageExtension", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Bo<PERSON>an", "java.lang.Object"]}]}, {"type": "sun.security.x509.NetscapeCertTypeExtension", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Bo<PERSON>an", "java.lang.Object"]}]}, {"type": "sun.security.x509.PrivateKeyUsageExtension", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Bo<PERSON>an", "java.lang.Object"]}]}, {"type": "sun.security.x509.SubjectAlternativeNameExtension", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Bo<PERSON>an", "java.lang.Object"]}]}, {"type": "sun.security.x509.SubjectKeyIdentifierExtension", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Bo<PERSON>an", "java.lang.Object"]}]}, {"type": {"proxy": ["java.lang.reflect.GenericArrayType", "org.springframework.core.SerializableTypeWrapper$SerializableTypeProxy", "java.io.Serializable"]}}, {"type": {"proxy": ["java.lang.reflect.ParameterizedType", "org.springframework.core.SerializableTypeWrapper$SerializableTypeProxy", "java.io.Serializable"]}}, {"type": {"proxy": ["java.lang.reflect.TypeVariable", "org.springframework.core.SerializableTypeWrapper$SerializableTypeProxy", "java.io.Serializable"]}}, {"type": {"proxy": ["java.lang.reflect.WildcardType", "org.springframework.core.SerializableTypeWrapper$SerializableTypeProxy", "java.io.Serializable"]}}, {"type": {"proxy": ["java.sql.Connection"]}}, {"type": {"proxy": ["org.springframework.amqp.rabbit.connection.ChannelProxy"]}}, {"type": {"proxy": ["org.springframework.beans.factory.annotation.Qualifier"]}}, {"type": {"proxy": ["org.springframework.boot.SpringBootConfiguration"]}}, {"type": {"proxy": ["org.springframework.boot.context.properties.ConfigurationProperties"]}}, {"type": {"proxy": ["org.springframework.web.bind.annotation.RequestMapping"]}}, {"type": {"proxy": ["org.springframework.web.bind.annotation.RequestParam"]}}], "resources": [{"glob": "META-INF/MANIFEST.MF"}, {"glob": "META-INF/build-info.properties"}, {"glob": "META-INF/resources/index.html"}, {"glob": "META-INF/services/ch.qos.logback.classic.spi.Configurator"}, {"glob": "META-INF/services/io.jsonwebtoken.io.Deserializer"}, {"glob": "META-INF/services/java.lang.System$LoggerFinder"}, {"glob": "META-INF/services/java.net.spi.InetAddressResolverProvider"}, {"glob": "META-INF/services/java.net.spi.URLStreamHandlerProvider"}, {"glob": "META-INF/services/java.nio.channels.spi.SelectorProvider"}, {"glob": "META-INF/services/java.sql.Driver"}, {"glob": "META-INF/services/java.time.zone.ZoneRulesProvider"}, {"glob": "META-INF/services/java.util.spi.ResourceBundleControlProvider"}, {"glob": "META-INF/services/javax.json.spi.JsonProvider"}, {"glob": "META-INF/services/org.apache.commons.logging.LogFactory"}, {"glob": "META-INF/services/org.apache.juli.logging.Log"}, {"glob": "META-INF/services/org.flywaydb.core.extensibility.Plugin"}, {"glob": "META-INF/services/org.slf4j.spi.SLF4JServiceProvider"}, {"glob": "META-INF/spring-autoconfigure-metadata.properties"}, {"glob": "META-INF/spring.components"}, {"glob": "META-INF/spring.factories"}, {"glob": "META-INF/spring.integration.properties"}, {"glob": "META-INF/spring/org.springframework.boot.autoconfigure.AutoConfiguration.imports"}, {"glob": "META-INF/spring/org.springframework.boot.autoconfigure.AutoConfiguration.replacements"}, {"glob": "application-default.properties"}, {"glob": "application-default.xml"}, {"glob": "application-default.yaml"}, {"glob": "application-default.yml"}, {"glob": "application.properties"}, {"glob": "application.xml"}, {"glob": "application.yaml"}, {"glob": "application.yml"}, {"glob": "banner.txt"}, {"glob": "char.def"}, {"glob": "com/munetmo/lingetic/"}, {"glob": "com/munetmo/lingetic/HealthService/infra/HTTP/HealthServiceController.class"}, {"glob": "com/munetmo/lingetic/LanguageService/infra/HTTP/LanguageServiceController.class"}, {"glob": "com/munetmo/lingetic/LanguageTestService/infra/Beans.class"}, {"glob": "com/munetmo/lingetic/LanguageTestService/infra/HTTP/LanguageTestServiceController.class"}, {"glob": "com/munetmo/lingetic/infra/auth/ClerkAuthenticationFilter.class"}, {"glob": "com/munetmo/lingetic/infra/auth/SecurityConfig.class"}, {"glob": "com/munetmo/lingetic/infra/database/PostgresDatabaseConfig.class"}, {"glob": "com/munetmo/lingetic/infra/tasks/RabbitMQConfig.class"}, {"glob": "commons-logging.properties"}, {"glob": "config/application-default.properties"}, {"glob": "config/application-default.xml"}, {"glob": "config/application-default.yaml"}, {"glob": "config/application-default.yml"}, {"glob": "config/application.properties"}, {"glob": "config/application.xml"}, {"glob": "config/application.yaml"}, {"glob": "config/application.yml"}, {"glob": "data-all.sql"}, {"glob": "data.sql"}, {"glob": "db/callback"}, {"glob": "db/migration"}, {"glob": "db/migration/V10__Add_Word_Explanations_Table.sql"}, {"glob": "db/migration/V11__Add_Sentence_ID_To_Questions_Table.sql"}, {"glob": "db/migration/V12__Add_Sentence_Reviews.sql"}, {"glob": "db/migration/V13__Add_Difficulty_To_Sentences_Table.sql"}, {"glob": "db/migration/V14__Remove_Question_Lists.sql"}, {"glob": "db/migration/V15__Add_Source_Word_Explanation_To_Questions.sql"}, {"glob": "db/migration/V16__Add_Foreign_Key_To_Sentence_Reviews_Language.sql"}, {"glob": "db/migration/V1__Create_Tables.sql"}, {"glob": "db/migration/V2__Create_Question_Lists_Table.sql"}, {"glob": "db/migration/V3__Make_Languages_Table_And_Add_French_Language.sql"}, {"glob": "db/migration/V4__Change_Difficulty_To_Int.sql"}, {"glob": "db/migration/V5__Add_Swedish_Language.sql"}, {"glob": "db/migration/V6__Add_Japanese_Language.sql"}, {"glob": "db/migration/V7__Add_Japanese_Modified_Hepburn_Language.sql"}, {"glob": "db/migration/V8__Add_SourceToTargetTranslation_Question_Type.sql"}, {"glob": "db/migration/V9__Create_Sentences_Table.sql"}, {"glob": "dictionaries/system_full.dic"}, {"glob": "git.properties"}, {"glob": "io/sentry/spring/boot/jakarta/SentryAutoConfiguration$HubConfiguration$ApacheHttpClientTransportFactoryAutoconfiguration.class"}, {"glob": "io/sentry/spring/boot/jakarta/SentryAutoConfiguration$HubConfiguration$ContextTagsEventProcessorConfiguration.class"}, {"glob": "io/sentry/spring/boot/jakarta/SentryAutoConfiguration$HubConfiguration$Graphql22Configuration.class"}, {"glob": "io/sentry/spring/boot/jakarta/SentryAutoConfiguration$HubConfiguration$GraphqlConfiguration.class"}, {"glob": "io/sentry/spring/boot/jakarta/SentryAutoConfiguration$HubConfiguration$OpenTelemetryAgentWithoutAutoInitConfiguration.class"}, {"glob": "io/sentry/spring/boot/jakarta/SentryAutoConfiguration$HubConfiguration$OpenTelemetryNoAgentConfiguration.class"}, {"glob": "io/sentry/spring/boot/jakarta/SentryAutoConfiguration$HubConfiguration$QuartzConfiguration.class"}, {"glob": "io/sentry/spring/boot/jakarta/SentryAutoConfiguration$HubConfiguration$SentryCheckInAspectsConfiguration.class"}, {"glob": "io/sentry/spring/boot/jakarta/SentryAutoConfiguration$HubConfiguration$SentryErrorAspectsConfiguration.class"}, {"glob": "io/sentry/spring/boot/jakarta/SentryAutoConfiguration$HubConfiguration$SentryPerformanceAspectsConfiguration.class"}, {"glob": "io/sentry/spring/boot/jakarta/SentryAutoConfiguration$HubConfiguration$SentryPerformanceRestTemplateConfiguration.class"}, {"glob": "io/sentry/spring/boot/jakarta/SentryAutoConfiguration$HubConfiguration$SentryPerformanceWebClientConfiguration.class"}, {"glob": "io/sentry/spring/boot/jakarta/SentryAutoConfiguration$HubConfiguration$SentrySpanRestClientConfiguration.class"}, {"glob": "io/sentry/spring/boot/jakarta/SentryAutoConfiguration$HubConfiguration$SentryWebMvcConfiguration$SentryMvcModeConfig.class"}, {"glob": "io/sentry/spring/boot/jakarta/SentryAutoConfiguration$HubConfiguration$SentryWebMvcConfiguration$SentrySecurityConfiguration.class"}, {"glob": "io/sentry/spring/boot/jakarta/SentryAutoConfiguration$HubConfiguration$SentryWebMvcConfiguration$SentryServletModeConfig.class"}, {"glob": "io/sentry/spring/boot/jakarta/SentryAutoConfiguration$HubConfiguration$SentryWebMvcConfiguration.class"}, {"glob": "io/sentry/spring/boot/jakarta/SentryAutoConfiguration$HubConfiguration.class"}, {"glob": "io/sentry/spring/boot/jakarta/SentryAutoConfiguration$SentryTracingCondition.class"}, {"glob": "io/sentry/spring/boot/jakarta/SentryAutoConfiguration$SpringProfilesEventProcessorConfiguration.class"}, {"glob": "io/sentry/spring/boot/jakarta/SentryAutoConfiguration.class"}, {"glob": "io/sentry/spring/boot/jakarta/SentryLogbackAppenderAutoConfiguration.class"}, {"glob": "io/sentry/spring/jakarta/SentryWebConfiguration.class"}, {"glob": "jakarta/servlet/Filter.class"}, {"glob": "jakarta/servlet/LocalStrings.properties"}, {"glob": "jakarta/servlet/LocalStrings_en.properties"}, {"glob": "jakarta/servlet/LocalStrings_en_US.properties"}, {"glob": "jakarta/servlet/http/LocalStrings.properties"}, {"glob": "jakarta/servlet/http/LocalStrings_en.properties"}, {"glob": "jakarta/servlet/http/LocalStrings_en_US.properties"}, {"glob": "jndi.properties"}, {"glob": "logback-spring.groovy"}, {"glob": "logback-spring.xml"}, {"glob": "logback-test-spring.groovy"}, {"glob": "logback-test-spring.xml"}, {"glob": "logback-test.groovy"}, {"glob": "logback-test.scmo"}, {"glob": "logback-test.xml"}, {"glob": "logback.groovy"}, {"glob": "logback.scmo"}, {"glob": "logback.xml"}, {"glob": "messages.properties"}, {"glob": "org/apache/catalina/authenticator/LocalStrings.properties"}, {"glob": "org/apache/catalina/authenticator/jaspic/LocalStrings.properties"}, {"glob": "org/apache/catalina/connector/LocalStrings.properties"}, {"glob": "org/apache/catalina/core/LocalStrings.properties"}, {"glob": "org/apache/catalina/core/RestrictedFilters.properties"}, {"glob": "org/apache/catalina/core/RestrictedListeners.properties"}, {"glob": "org/apache/catalina/core/RestrictedServlets.properties"}, {"glob": "org/apache/catalina/deploy/LocalStrings.properties"}, {"glob": "org/apache/catalina/loader/JdbcLeakPrevention.class"}, {"glob": "org/apache/catalina/loader/LocalStrings.properties"}, {"glob": "org/apache/catalina/mapper/LocalStrings.properties"}, {"glob": "org/apache/catalina/mbeans/LocalStrings.properties"}, {"glob": "org/apache/catalina/realm/LocalStrings.properties"}, {"glob": "org/apache/catalina/security/LocalStrings.properties"}, {"glob": "org/apache/catalina/session/LocalStrings.properties"}, {"glob": "org/apache/catalina/startup/LocalStrings.properties"}, {"glob": "org/apache/catalina/util/CharsetMapperDefault.properties"}, {"glob": "org/apache/catalina/util/LocalStrings.properties"}, {"glob": "org/apache/catalina/util/ServerInfo.properties"}, {"glob": "org/apache/catalina/valves/LocalStrings.properties"}, {"glob": "org/apache/catalina/webresources/LocalStrings.properties"}, {"glob": "org/apache/coyote/LocalStrings.properties"}, {"glob": "org/apache/coyote/http11/LocalStrings.properties"}, {"glob": "org/apache/coyote/http11/filters/LocalStrings.properties"}, {"glob": "org/apache/naming/LocalStrings.properties"}, {"glob": "org/apache/naming/LocalStrings_en.properties"}, {"glob": "org/apache/naming/LocalStrings_en_US.properties"}, {"glob": "org/apache/tomcat/util/LocalStrings.properties"}, {"glob": "org/apache/tomcat/util/buf/LocalStrings.properties"}, {"glob": "org/apache/tomcat/util/compat/LocalStrings.properties"}, {"glob": "org/apache/tomcat/util/descriptor/web/LocalStrings.properties"}, {"glob": "org/apache/tomcat/util/http/LocalStrings.properties"}, {"glob": "org/apache/tomcat/util/http/parser/LocalStrings.properties"}, {"glob": "org/apache/tomcat/util/modeler/LocalStrings.properties"}, {"glob": "org/apache/tomcat/util/net/LocalStrings.properties"}, {"glob": "org/apache/tomcat/util/scan/LocalStrings.properties"}, {"glob": "org/apache/tomcat/util/threads/LocalStrings.properties"}, {"glob": "org/apache/tomcat/websocket/LocalStrings.properties"}, {"glob": "org/apache/tomcat/websocket/server/LocalStrings.properties"}, {"glob": "org/flywaydb/core/internal/version.txt"}, {"glob": "org/postgresql/driverconfig.properties"}, {"glob": "org/springframework/amqp/rabbit/annotation/EnableRabbit.class"}, {"glob": "org/springframework/amqp/rabbit/annotation/MultiRabbitBootstrapConfiguration.class"}, {"glob": "org/springframework/amqp/rabbit/annotation/RabbitBootstrapConfiguration.class"}, {"glob": "org/springframework/amqp/rabbit/annotation/RabbitListenerConfigurationSelector.class"}, {"glob": "org/springframework/aot/hint/annotation/Reflective.class"}, {"glob": "org/springframework/beans/factory/Aware.class"}, {"glob": "org/springframework/beans/factory/BeanClassLoaderAware.class"}, {"glob": "org/springframework/beans/factory/BeanFactoryAware.class"}, {"glob": "org/springframework/beans/factory/BeanNameAware.class"}, {"glob": "org/springframework/beans/factory/DisposableBean.class"}, {"glob": "org/springframework/beans/factory/InitializingBean.class"}, {"glob": "org/springframework/beans/factory/config/BeanFactoryPostProcessor.class"}, {"glob": "org/springframework/boot/autoconfigure/AbstractDependsOnBeanFactoryPostProcessor.class"}, {"glob": "org/springframework/boot/autoconfigure/AutoConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/AutoConfigureAfter.class"}, {"glob": "org/springframework/boot/autoconfigure/AutoConfigureBefore.class"}, {"glob": "org/springframework/boot/autoconfigure/AutoConfigureOrder.class"}, {"glob": "org/springframework/boot/autoconfigure/admin/SpringApplicationAdminJmxAutoConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/amqp/RabbitAnnotationDrivenConfiguration$EnableRabbitConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/amqp/RabbitAnnotationDrivenConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/amqp/RabbitAutoConfiguration$MessagingTemplateConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/amqp/RabbitAutoConfiguration$RabbitConnectionFactoryCreator.class"}, {"glob": "org/springframework/boot/autoconfigure/amqp/RabbitAutoConfiguration$RabbitTemplateConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/amqp/RabbitAutoConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/amqp/RabbitStreamConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/aop/AopAutoConfiguration$AspectJAutoProxyingConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/aop/AopAutoConfiguration$ClassProxyingConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/aop/AopAutoConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/availability/ApplicationAvailabilityAutoConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/cache/CacheAutoConfiguration$CacheConfigurationImportSelector.class"}, {"glob": "org/springframework/boot/autoconfigure/cache/CacheAutoConfiguration$CacheManagerEntityManagerFactoryDependsOnPostProcessor.class"}, {"glob": "org/springframework/boot/autoconfigure/cache/CacheAutoConfiguration$CacheManagerValidator.class"}, {"glob": "org/springframework/boot/autoconfigure/cache/CacheAutoConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/cache/GenericCacheConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/cache/NoOpCacheConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/cache/SimpleCacheConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/condition/ConditionalOnBean.class"}, {"glob": "org/springframework/boot/autoconfigure/condition/ConditionalOnClass.class"}, {"glob": "org/springframework/boot/autoconfigure/condition/ConditionalOnMissingBean.class"}, {"glob": "org/springframework/boot/autoconfigure/condition/ConditionalOnMissingClass.class"}, {"glob": "org/springframework/boot/autoconfigure/condition/ConditionalOnNotWarDeployment.class"}, {"glob": "org/springframework/boot/autoconfigure/condition/ConditionalOnProperty.class"}, {"glob": "org/springframework/boot/autoconfigure/condition/ConditionalOnSingleCandidate.class"}, {"glob": "org/springframework/boot/autoconfigure/condition/ConditionalOnWebApplication.class"}, {"glob": "org/springframework/boot/autoconfigure/context/ConfigurationPropertiesAutoConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/context/LifecycleAutoConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/context/MessageSourceAutoConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/context/PropertyPlaceholderAutoConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/dao/PersistenceExceptionTranslationAutoConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$Extension.class"}, {"glob": "org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayAutoConfigurationRuntimeHints.class"}, {"glob": "org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayDataSourceCondition$DataSourceBeanCondition.class"}, {"glob": "org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayDataSourceCondition$FlywayUrlCondition.class"}, {"glob": "org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayDataSourceCondition$JdbcConnectionDetailsCondition.class"}, {"glob": "org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$FlywayDataSourceCondition.class"}, {"glob": "org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$LocationResolver.class"}, {"glob": "org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$OracleFlywayConfigurationCustomizer.class"}, {"glob": "org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$PostgresqlFlywayConfigurationCustomizer.class"}, {"glob": "org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$PropertiesFlywayConnectionDetails.class"}, {"glob": "org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$SqlServerFlywayConfigurationCustomizer.class"}, {"glob": "org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration$StringOrNumberToMigrationVersionConverter.class"}, {"glob": "org/springframework/boot/autoconfigure/flyway/FlywayAutoConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/http/GsonHttpMessageConvertersConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/http/HttpMessageConvertersAutoConfiguration$HttpMessageConvertersAutoConfigurationRuntimeHints.class"}, {"glob": "org/springframework/boot/autoconfigure/http/HttpMessageConvertersAutoConfiguration$NotReactiveWebApplicationCondition$ReactiveWebApplication.class"}, {"glob": "org/springframework/boot/autoconfigure/http/HttpMessageConvertersAutoConfiguration$NotReactiveWebApplicationCondition.class"}, {"glob": "org/springframework/boot/autoconfigure/http/HttpMessageConvertersAutoConfiguration$StringHttpMessageConverterConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/http/HttpMessageConvertersAutoConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/http/JacksonHttpMessageConvertersConfiguration$MappingJackson2HttpMessageConverterConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/http/JacksonHttpMessageConvertersConfiguration$MappingJackson2XmlHttpMessageConverterConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/http/JacksonHttpMessageConvertersConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/http/JsonbHttpMessageConvertersConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/http/client/HttpClientAutoConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/http/client/NotReactiveWebApplicationCondition$ReactiveWebApplication.class"}, {"glob": "org/springframework/boot/autoconfigure/http/client/NotReactiveWebApplicationCondition.class"}, {"glob": "org/springframework/boot/autoconfigure/info/ProjectInfoAutoConfiguration$GitResourceAvailableCondition.class"}, {"glob": "org/springframework/boot/autoconfigure/info/ProjectInfoAutoConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/jackson/JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration$StandardJackson2ObjectMapperBuilderCustomizer.class"}, {"glob": "org/springframework/boot/autoconfigure/jackson/JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/jackson/JacksonAutoConfiguration$JacksonAutoConfigurationRuntimeHints.class"}, {"glob": "org/springframework/boot/autoconfigure/jackson/JacksonAutoConfiguration$JacksonMixinConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/jackson/JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/jackson/JacksonAutoConfiguration$JacksonObjectMapperConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/jackson/JacksonAutoConfiguration$ParameterNamesModuleConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/jackson/JacksonAutoConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/jdbc/DataSourceAutoConfiguration$EmbeddedDatabaseCondition.class"}, {"glob": "org/springframework/boot/autoconfigure/jdbc/DataSourceAutoConfiguration$EmbeddedDatabaseConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/jdbc/DataSourceAutoConfiguration$PooledDataSourceAvailableCondition.class"}, {"glob": "org/springframework/boot/autoconfigure/jdbc/DataSourceAutoConfiguration$PooledDataSourceCondition$ExplicitType.class"}, {"glob": "org/springframework/boot/autoconfigure/jdbc/DataSourceAutoConfiguration$PooledDataSourceCondition$PooledDataSourceAvailable.class"}, {"glob": "org/springframework/boot/autoconfigure/jdbc/DataSourceAutoConfiguration$PooledDataSourceCondition.class"}, {"glob": "org/springframework/boot/autoconfigure/jdbc/DataSourceAutoConfiguration$PooledDataSourceConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/jdbc/DataSourceAutoConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/jdbc/DataSourceCheckpointRestoreConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/jdbc/DataSourceConfiguration$Dbcp2.class"}, {"glob": "org/springframework/boot/autoconfigure/jdbc/DataSourceConfiguration$Generic.class"}, {"glob": "org/springframework/boot/autoconfigure/jdbc/DataSourceConfiguration$Hikari.class"}, {"glob": "org/springframework/boot/autoconfigure/jdbc/DataSourceConfiguration$OracleUcp.class"}, {"glob": "org/springframework/boot/autoconfigure/jdbc/DataSourceConfiguration$Tomcat.class"}, {"glob": "org/springframework/boot/autoconfigure/jdbc/DataSourceJmxConfiguration$Hikari.class"}, {"glob": "org/springframework/boot/autoconfigure/jdbc/DataSourceJmxConfiguration$TomcatDataSourceJmxConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/jdbc/DataSourceJmxConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/jdbc/DataSourceTransactionManagerAutoConfiguration$JdbcTransactionManagerConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/jdbc/DataSourceTransactionManagerAutoConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/jdbc/JdbcClientAutoConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/jdbc/JdbcTemplateAutoConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/jdbc/JdbcTemplateConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/jdbc/JndiDataSourceAutoConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/jdbc/NamedParameterJdbcTemplateConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/jdbc/metadata/DataSourcePoolMetadataProvidersConfiguration$CommonsDbcp2PoolDataSourceMetadataProviderConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/jdbc/metadata/DataSourcePoolMetadataProvidersConfiguration$HikariPoolDataSourceMetadataProviderConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/jdbc/metadata/DataSourcePoolMetadataProvidersConfiguration$OracleUcpPoolDataSourceMetadataProviderConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/jdbc/metadata/DataSourcePoolMetadataProvidersConfiguration$TomcatDataSourcePoolMetadataProviderConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/jdbc/metadata/DataSourcePoolMetadataProvidersConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/jmx/JmxAutoConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/orm/jpa/EntityManagerFactoryDependsOnPostProcessor.class"}, {"glob": "org/springframework/boot/autoconfigure/security/ConditionalOnDefaultWebSecurity.class"}, {"glob": "org/springframework/boot/autoconfigure/security/DefaultWebSecurityCondition$Beans.class"}, {"glob": "org/springframework/boot/autoconfigure/security/DefaultWebSecurityCondition$Classes.class"}, {"glob": "org/springframework/boot/autoconfigure/security/DefaultWebSecurityCondition.class"}, {"glob": "org/springframework/boot/autoconfigure/security/SecurityDataConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/security/reactive/ReactiveUserDetailsServiceAutoConfiguration$MissingAlternativeOrUserPropertiesConfigured$MissingAlternative.class"}, {"glob": "org/springframework/boot/autoconfigure/security/reactive/ReactiveUserDetailsServiceAutoConfiguration$MissingAlternativeOrUserPropertiesConfigured$NameConfigured.class"}, {"glob": "org/springframework/boot/autoconfigure/security/reactive/ReactiveUserDetailsServiceAutoConfiguration$MissingAlternativeOrUserPropertiesConfigured$PasswordConfigured.class"}, {"glob": "org/springframework/boot/autoconfigure/security/reactive/ReactiveUserDetailsServiceAutoConfiguration$MissingAlternativeOrUserPropertiesConfigured.class"}, {"glob": "org/springframework/boot/autoconfigure/security/reactive/ReactiveUserDetailsServiceAutoConfiguration$RSocketEnabledOrReactiveWebApplication$RSocketSecurityEnabledCondition.class"}, {"glob": "org/springframework/boot/autoconfigure/security/reactive/ReactiveUserDetailsServiceAutoConfiguration$RSocketEnabledOrReactiveWebApplication$ReactiveWebApplicationCondition.class"}, {"glob": "org/springframework/boot/autoconfigure/security/reactive/ReactiveUserDetailsServiceAutoConfiguration$RSocketEnabledOrReactiveWebApplication.class"}, {"glob": "org/springframework/boot/autoconfigure/security/reactive/ReactiveUserDetailsServiceAutoConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/security/servlet/SecurityAutoConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/security/servlet/SecurityFilterAutoConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/security/servlet/SpringBootWebSecurityConfiguration$SecurityFilterChainConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/security/servlet/SpringBootWebSecurityConfiguration$WebSecurityEnablerConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/security/servlet/SpringBootWebSecurityConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/sql/init/DataSourceInitializationConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/sql/init/R2dbcInitializationConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/sql/init/SqlInitializationAutoConfiguration$SqlInitializationModeCondition$ModeIsNever.class"}, {"glob": "org/springframework/boot/autoconfigure/sql/init/SqlInitializationAutoConfiguration$SqlInitializationModeCondition.class"}, {"glob": "org/springframework/boot/autoconfigure/sql/init/SqlInitializationAutoConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/ssl/SslAutoConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/task/TaskExecutionAutoConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/task/TaskExecutorConfigurations$SimpleAsyncTaskExecutorBuilderConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/task/TaskExecutorConfigurations$TaskExecutorConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/task/TaskExecutorConfigurations$ThreadPoolTaskExecutorBuilderConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/task/TaskSchedulingAutoConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/task/TaskSchedulingConfigurations$SimpleAsyncTaskSchedulerBuilderConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/task/TaskSchedulingConfigurations$TaskSchedulerConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/task/TaskSchedulingConfigurations$ThreadPoolTaskSchedulerBuilderConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/transaction/TransactionAutoConfiguration$AspectJTransactionManagementConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/transaction/TransactionAutoConfiguration$EnableTransactionManagementConfiguration$CglibAutoProxyConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/transaction/TransactionAutoConfiguration$EnableTransactionManagementConfiguration$JdkDynamicAutoProxyConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/transaction/TransactionAutoConfiguration$EnableTransactionManagementConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/transaction/TransactionAutoConfiguration$TransactionTemplateConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/transaction/TransactionAutoConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/transaction/TransactionManagerCustomizationAutoConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/web/client/NotReactiveWebApplicationCondition$ReactiveWebApplication.class"}, {"glob": "org/springframework/boot/autoconfigure/web/client/NotReactiveWebApplicationCondition.class"}, {"glob": "org/springframework/boot/autoconfigure/web/client/RestClientAutoConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/web/client/RestTemplateAutoConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/web/embedded/EmbeddedWebServerFactoryCustomizerAutoConfiguration$JettyWebServerFactoryCustomizerConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/web/embedded/EmbeddedWebServerFactoryCustomizerAutoConfiguration$NettyWebServerFactoryCustomizerConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/web/embedded/EmbeddedWebServerFactoryCustomizerAutoConfiguration$TomcatWebServerFactoryCustomizerConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/web/embedded/EmbeddedWebServerFactoryCustomizerAutoConfiguration$UndertowWebServerFactoryCustomizerConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/web/embedded/EmbeddedWebServerFactoryCustomizerAutoConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/web/servlet/DispatcherServletAutoConfiguration$DefaultDispatcherServletCondition.class"}, {"glob": "org/springframework/boot/autoconfigure/web/servlet/DispatcherServletAutoConfiguration$DispatcherServletConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/web/servlet/DispatcherServletAutoConfiguration$DispatcherServletRegistrationCondition.class"}, {"glob": "org/springframework/boot/autoconfigure/web/servlet/DispatcherServletAutoConfiguration$DispatcherServletRegistrationConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/web/servlet/DispatcherServletAutoConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/web/servlet/HttpEncodingAutoConfiguration$LocaleCharsetMappingsCustomizer.class"}, {"glob": "org/springframework/boot/autoconfigure/web/servlet/HttpEncodingAutoConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/web/servlet/MultipartAutoConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/web/servlet/ServletWebServerFactoryAutoConfiguration$BeanPostProcessorsRegistrar.class"}, {"glob": "org/springframework/boot/autoconfigure/web/servlet/ServletWebServerFactoryAutoConfiguration$ForwardedHeaderFilterConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/web/servlet/ServletWebServerFactoryAutoConfiguration$ForwardedHeaderFilterCustomizer.class"}, {"glob": "org/springframework/boot/autoconfigure/web/servlet/ServletWebServerFactoryAutoConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/web/servlet/ServletWebServerFactoryConfiguration$EmbeddedJetty.class"}, {"glob": "org/springframework/boot/autoconfigure/web/servlet/ServletWebServerFactoryConfiguration$EmbeddedTomcat.class"}, {"glob": "org/springframework/boot/autoconfigure/web/servlet/ServletWebServerFactoryConfiguration$EmbeddedUndertow.class"}, {"glob": "org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$OptionalPathExtensionContentNegotiationStrategy.class"}, {"glob": "org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$ProblemDetailsErrorHandlingConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$ResourceChainCustomizerConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$ResourceChainResourceHandlerRegistrationCustomizer.class"}, {"glob": "org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$ResourceHandlerRegistrationCustomizer.class"}, {"glob": "org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter.class"}, {"glob": "org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$WelcomePageHandlerMappingFactory.class"}, {"glob": "org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/web/servlet/error/ErrorMvcAutoConfiguration$DefaultErrorViewResolverConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/web/servlet/error/ErrorMvcAutoConfiguration$ErrorPageCustomizer.class"}, {"glob": "org/springframework/boot/autoconfigure/web/servlet/error/ErrorMvcAutoConfiguration$ErrorTemplateMissingCondition.class"}, {"glob": "org/springframework/boot/autoconfigure/web/servlet/error/ErrorMvcAutoConfiguration$PreserveErrorControllerTargetClassPostProcessor.class"}, {"glob": "org/springframework/boot/autoconfigure/web/servlet/error/ErrorMvcAutoConfiguration$StaticView.class"}, {"glob": "org/springframework/boot/autoconfigure/web/servlet/error/ErrorMvcAutoConfiguration$WhitelabelErrorViewConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/web/servlet/error/ErrorMvcAutoConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/websocket/servlet/WebSocketServletAutoConfiguration$JettyWebSocketConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/websocket/servlet/WebSocketServletAutoConfiguration$TomcatWebSocketConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/websocket/servlet/WebSocketServletAutoConfiguration$UndertowWebSocketConfiguration.class"}, {"glob": "org/springframework/boot/autoconfigure/websocket/servlet/WebSocketServletAutoConfiguration.class"}, {"glob": "org/springframework/boot/context/properties/EnableConfigurationProperties.class"}, {"glob": "org/springframework/boot/context/properties/EnableConfigurationPropertiesRegistrar.class"}, {"glob": "org/springframework/boot/sql/init/dependency/DatabaseInitializationDependencyConfigurer.class"}, {"glob": "org/springframework/context/ApplicationContextAware.class"}, {"glob": "org/springframework/context/EnvironmentAware.class"}, {"glob": "org/springframework/context/ResourceLoaderAware.class"}, {"glob": "org/springframework/context/annotation/AdviceModeImportSelector.class"}, {"glob": "org/springframework/context/annotation/AutoProxyRegistrar.class"}, {"glob": "org/springframework/context/annotation/Conditional.class"}, {"glob": "org/springframework/context/annotation/Configuration.class"}, {"glob": "org/springframework/context/annotation/DeferredImportSelector.class"}, {"glob": "org/springframework/context/annotation/Import.class"}, {"glob": "org/springframework/context/annotation/ImportAware.class"}, {"glob": "org/springframework/context/annotation/ImportBeanDefinitionRegistrar.class"}, {"glob": "org/springframework/context/annotation/ImportRuntimeHints.class"}, {"glob": "org/springframework/context/annotation/Role.class"}, {"glob": "org/springframework/core/Ordered.class"}, {"glob": "org/springframework/core/annotation/Order.class"}, {"glob": "org/springframework/core/env/EnvironmentCapable.class"}, {"glob": "org/springframework/security/config/annotation/authentication/configuration/AuthenticationConfiguration$AuthenticationManagerDelegator.class"}, {"glob": "org/springframework/security/config/annotation/authentication/configuration/AuthenticationConfiguration$DefaultPasswordEncoderAuthenticationManagerBuilder.class"}, {"glob": "org/springframework/security/config/annotation/authentication/configuration/AuthenticationConfiguration$EnableGlobalAuthenticationAutowiredConfigurer.class"}, {"glob": "org/springframework/security/config/annotation/authentication/configuration/AuthenticationConfiguration$LazyPasswordEncoder.class"}, {"glob": "org/springframework/security/config/annotation/authentication/configuration/AuthenticationConfiguration.class"}, {"glob": "org/springframework/security/config/annotation/authentication/configuration/EnableGlobalAuthentication.class"}, {"glob": "org/springframework/security/config/annotation/configuration/ObjectPostProcessorConfiguration.class"}, {"glob": "org/springframework/security/config/annotation/web/configuration/EnableWebSecurity.class"}, {"glob": "org/springframework/security/config/annotation/web/configuration/HttpSecurityConfiguration$DefaultPasswordEncoderAuthenticationManagerBuilder.class"}, {"glob": "org/springframework/security/config/annotation/web/configuration/HttpSecurityConfiguration$LazyPasswordEncoder.class"}, {"glob": "org/springframework/security/config/annotation/web/configuration/HttpSecurityConfiguration.class"}, {"glob": "org/springframework/security/config/annotation/web/configuration/OAuth2ImportSelector.class"}, {"glob": "org/springframework/security/config/annotation/web/configuration/ObservationConfiguration.class"}, {"glob": "org/springframework/security/config/annotation/web/configuration/ObservationImportSelector.class"}, {"glob": "org/springframework/security/config/annotation/web/configuration/SpringWebMvcImportSelector.class"}, {"glob": "org/springframework/security/config/annotation/web/configuration/WebMvcSecurityConfiguration.class"}, {"glob": "org/springframework/security/config/annotation/web/configuration/WebSecurityConfiguration$AnnotationAwareOrderComparator.class"}, {"glob": "org/springframework/security/config/annotation/web/configuration/WebSecurityConfiguration.class"}, {"glob": "org/springframework/transaction/annotation/AbstractTransactionManagementConfiguration.class"}, {"glob": "org/springframework/transaction/annotation/EnableTransactionManagement.class"}, {"glob": "org/springframework/transaction/annotation/ProxyTransactionManagementConfiguration.class"}, {"glob": "org/springframework/transaction/annotation/TransactionManagementConfigurationSelector.class"}, {"glob": "org/springframework/web/bind/annotation/Mapping.class"}, {"glob": "org/springframework/web/bind/annotation/RequestMapping.class"}, {"glob": "org/springframework/web/bind/annotation/ResponseBody.class"}, {"glob": "org/springframework/web/bind/annotation/RestController.class"}, {"glob": "org/springframework/web/context/ServletContextAware.class"}, {"glob": "org/springframework/web/filter/GenericFilterBean$FilterConfigPropertyValues.class"}, {"glob": "org/springframework/web/filter/GenericFilterBean.class"}, {"glob": "org/springframework/web/filter/OncePerRequestFilter.class"}, {"glob": "org/springframework/web/servlet/config/annotation/DelegatingWebMvcConfiguration.class"}, {"glob": "org/springframework/web/servlet/config/annotation/WebMvcConfigurationSupport$NoOpValidator.class"}, {"glob": "org/springframework/web/servlet/config/annotation/WebMvcConfigurationSupport.class"}, {"glob": "org/springframework/web/servlet/config/annotation/WebMvcConfigurer.class"}, {"glob": "public/index.html"}, {"glob": "rabbitmq-amqp-client.properties"}, {"glob": "resources/index.html"}, {"glob": "schema-all.sql"}, {"glob": "schema.sql"}, {"glob": "sentry-debug-meta.properties"}, {"glob": "sentry-external-modules.txt"}, {"glob": "sentry.properties"}, {"glob": "spring.properties"}, {"glob": "static/index.html"}, {"glob": "sudachi/sudachi.json"}, {"glob": "unk.def"}, {"module": "java.base", "glob": "jdk/internal/icu/impl/data/icudt74b/nfkc.nrm"}, {"module": "java.base", "glob": "jdk/internal/icu/impl/data/icudt74b/uprops.icu"}, {"module": "java.base", "glob": "sun/net/idn/uidna.spp"}], "bundles": [{"name": "jakarta.servlet.LocalStrings", "locales": ["en-US"]}, {"name": "jakarta.servlet.http.LocalStrings", "locales": ["en-US"]}, {"name": "org.apache.catalina.authenticator.LocalStrings", "locales": ["und"]}, {"name": "org.apache.catalina.authenticator.jaspic.LocalStrings", "locales": ["und"]}, {"name": "org.apache.catalina.connector.LocalStrings", "locales": ["und"]}, {"name": "org.apache.catalina.core.LocalStrings", "locales": ["und"]}, {"name": "org.apache.catalina.deploy.LocalStrings", "locales": ["und"]}, {"name": "org.apache.catalina.loader.LocalStrings", "locales": ["und"]}, {"name": "org.apache.catalina.mapper.LocalStrings", "locales": ["und"]}, {"name": "org.apache.catalina.mbeans.LocalStrings", "locales": ["und"]}, {"name": "org.apache.catalina.realm.LocalStrings", "locales": ["und"]}, {"name": "org.apache.catalina.security.LocalStrings", "locales": ["und"]}, {"name": "org.apache.catalina.session.LocalStrings", "locales": ["und"]}, {"name": "org.apache.catalina.startup.LocalStrings", "locales": ["und"]}, {"name": "org.apache.catalina.util.LocalStrings", "locales": ["und"]}, {"name": "org.apache.catalina.valves.LocalStrings", "locales": ["und"]}, {"name": "org.apache.catalina.webresources.LocalStrings", "locales": ["und"]}, {"name": "org.apache.coyote.LocalStrings", "locales": ["und"]}, {"name": "org.apache.coyote.http11.LocalStrings", "locales": ["und"]}, {"name": "org.apache.coyote.http11.filters.LocalStrings", "locales": ["und"]}, {"name": "org.apache.naming.LocalStrings", "locales": ["en-US"]}, {"name": "org.apache.tomcat.util.LocalStrings", "locales": ["und"]}, {"name": "org.apache.tomcat.util.buf.LocalStrings", "locales": ["und"]}, {"name": "org.apache.tomcat.util.compat.LocalStrings", "locales": ["und"]}, {"name": "org.apache.tomcat.util.descriptor.web.LocalStrings", "locales": ["und"]}, {"name": "org.apache.tomcat.util.http.LocalStrings", "locales": ["und"]}, {"name": "org.apache.tomcat.util.http.parser.LocalStrings", "locales": ["und"]}, {"name": "org.apache.tomcat.util.modeler.LocalStrings", "locales": ["und"]}, {"name": "org.apache.tomcat.util.net.LocalStrings", "locales": ["und"]}, {"name": "org.apache.tomcat.util.scan.LocalStrings", "locales": ["und"]}, {"name": "org.apache.tomcat.util.threads.LocalStrings", "locales": ["und"]}, {"name": "org.apache.tomcat.websocket.LocalStrings", "locales": ["und"]}, {"name": "org.apache.tomcat.websocket.server.LocalStrings", "locales": ["und"]}], "jni": [{"type": "java.lang.Bo<PERSON>an", "methods": [{"name": "getBoolean", "parameterTypes": ["java.lang.String"]}]}, {"type": "sun.management.VMManagementImpl", "fields": [{"name": "compTimeMonitoringSupport"}, {"name": "currentThreadCpuTimeSupport"}, {"name": "objectMonitorUsageSupport"}, {"name": "otherThreadCpuTimeSupport"}, {"name": "remoteDiagnosticCommandsSupport"}, {"name": "synchronizerUsageSupport"}, {"name": "threadAllocatedMemorySupport"}, {"name": "threadContentionMonitoringSupport"}]}]}