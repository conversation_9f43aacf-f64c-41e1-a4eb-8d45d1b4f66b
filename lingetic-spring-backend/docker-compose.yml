services:
  postgres:
    image: postgres:17-alpine
    environment:
      POSTGRES_DB: lingetic
      POSTGRES_USER: ${DATABASE_USERNAME:?DATABASE_USERNAME environment variable is not set}
      POSTGRES_PASSWORD: ${DATABASE_PASSWORD:?DATABASE_PASSWORD environment variable is not set}
    ports:
      - "5432:5432"
    volumes:
      - pgdata:/var/lib/postgresql/data

  rabbitmq:
    image: rabbitmq:4-management
    environment:
      - RABBITMQ_DEFAULT_USER=${RABBITMQ_USERNAME:?RABBITMQ_USERNAME environment variable is not set}
      - RABBITMQ_DEFAULT_PASS=${RABBITMQ_PASSWORD:?RABBITMQ_PASSWORD environment variable is not set}
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq

volumes:
  pgdata:
  rabbitmq_data:
